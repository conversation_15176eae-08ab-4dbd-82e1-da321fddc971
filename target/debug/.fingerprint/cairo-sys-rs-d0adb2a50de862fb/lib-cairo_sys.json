{"rustc": 11410426090777951712, "features": "[\"glib\", \"use_glib\"]", "declared_features": "[\"freetype\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"v1_18\", \"win32-surface\", \"winapi\", \"x11\", \"xcb\", \"xlib\"]", "target": 12604004911878344227, "profile": 15657897354478470176, "path": 9413903390968691562, "deps": [[4684437522915235464, "libc", false, 3560103487148467933], [6885242093860886281, "build_script_build", false, 12505201142096747024], [13626264195287554611, "glib", false, 7000208169251009841]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/cairo-sys-rs-d0adb2a50de862fb/dep-lib-cairo_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 11410426090777951712, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 7255260797386237516, "deps": [[555019317135488525, "regex_automata", false, 12736414652293637904], [2779309023524819297, "aho_corasick", false, 678153901425810231], [9408802513701742484, "regex_syntax", false, 258948347578229103], [15932120279885307830, "memchr", false, 9485884913290808845]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-e15ba7b72bbc3526/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 11410426090777951712, "features": "[\"default\", \"glib\", \"use_glib\"]", "declared_features": "[\"default\", \"freetype\", \"freetype-rs\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"v1_18\", \"win32-surface\", \"xcb\", \"xlib\"]", "target": 8694848923278475479, "profile": 15657897354478470176, "path": 6366911288659063010, "deps": [[3722963349756955755, "once_cell", false, 640558688517600718], [4684437522915235464, "libc", false, 3560103487148467933], [6885242093860886281, "ffi", false, 9233208073834427799], [7896293946984509699, "bitflags", false, 13717900489699398556], [7963079641721436784, "glib", false, 16318266526900073076], [8008191657135824715, "thiserror", false, 7623318381593251887]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/cairo-rs-8ec32711e3646cb5/dep-lib-cairo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
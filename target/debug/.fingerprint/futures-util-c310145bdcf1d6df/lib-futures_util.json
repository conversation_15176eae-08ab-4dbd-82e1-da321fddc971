{"rustc": 11410426090777951712, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 762770903016287571, "deps": [[1615478164327904835, "pin_utils", false, 4429706414687626893], [1906322745568073236, "pin_project_lite", false, 17083552785855022264], [5451793922601807560, "slab", false, 1609416406577997715], [7620660491849607393, "futures_core", false, 17564687540637978949], [10565019901765856648, "futures_macro", false, 9368125312466493167], [16240732885093539806, "futures_task", false, 2830520890067037601]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-c310145bdcf1d6df/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
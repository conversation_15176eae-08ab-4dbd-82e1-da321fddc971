{"rustc": 11410426090777951712, "features": "[\"alloc\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 762770903016287571, "deps": [[1615478164327904835, "pin_utils", false, 2150282903844117097], [1906322745568073236, "pin_project_lite", false, 18239671094481411079], [5451793922601807560, "slab", false, 10954321895628785311], [7620660491849607393, "futures_core", false, 1400831899005562926], [16240732885093539806, "futures_task", false, 4293834330771465597]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-util-f82a7d159207cc39/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
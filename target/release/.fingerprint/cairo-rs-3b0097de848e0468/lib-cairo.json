{"rustc": 11410426090777951712, "features": "[\"default\", \"glib\", \"use_glib\"]", "declared_features": "[\"default\", \"dox\", \"freetype\", \"freetype-crate\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"win32-surface\", \"xcb\", \"xlib\"]", "target": 8694848923278475479, "profile": 2040997289075261528, "path": 5975738850667503735, "deps": [[4684437522915235464, "libc", false, 7456781002432991227], [8008191657135824715, "thiserror", false, 12060785556011550822], [10435729446543529114, "bitflags", false, 16162628388266686511], [11957567580670249626, "ffi", false, 9265823613519391993], [12811985027253923950, "glib", false, 3666779732642781097]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cairo-rs-3b0097de848e0468/dep-lib-cairo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
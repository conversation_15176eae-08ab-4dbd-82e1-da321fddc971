{"rustc": 11410426090777951712, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 7255260797386237516, "deps": [[555019317135488525, "regex_automata", false, 15394595145005877691], [2779309023524819297, "aho_corasick", false, 7518111961854109513], [9408802513701742484, "regex_syntax", false, 15142413475226455730], [15932120279885307830, "memchr", false, 17567431629413757122]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-aeb0ee897d7930a7/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
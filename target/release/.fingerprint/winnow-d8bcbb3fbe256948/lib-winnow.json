{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 5451394256983680347, "path": 4809367042453274830, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-d8bcbb3fbe256948/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
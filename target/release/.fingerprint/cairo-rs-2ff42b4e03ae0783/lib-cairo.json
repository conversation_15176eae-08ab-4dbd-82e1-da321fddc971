{"rustc": 11410426090777951712, "features": "[\"default\", \"glib\", \"use_glib\"]", "declared_features": "[\"default\", \"freetype\", \"freetype-rs\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"v1_18\", \"win32-surface\", \"xcb\", \"xlib\"]", "target": 8694848923278475479, "profile": 2040997289075261528, "path": 6366911288659063010, "deps": [[3722963349756955755, "once_cell", false, 4676093175999713378], [4684437522915235464, "libc", false, 7456781002432991227], [6885242093860886281, "ffi", false, 18377856615531351241], [7896293946984509699, "bitflags", false, 15895190103943842241], [7963079641721436784, "glib", false, 16405734683188022069], [8008191657135824715, "thiserror", false, 12060785556011550822]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cairo-rs-2ff42b4e03ae0783/dep-lib-cairo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 11410426090777951712, "features": "[\"glib\", \"use_glib\"]", "declared_features": "[\"dox\", \"freetype\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"win32-surface\", \"winapi\", \"x11\", \"xcb\", \"xlib\"]", "target": 12604004911878344227, "profile": 2040997289075261528, "path": 10847395327077838713, "deps": [[4684437522915235464, "libc", false, 7456781002432991227], [5081559103449079038, "glib", false, 5486738644628033573], [11957567580670249626, "build_script_build", false, 12835122839193597497]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cairo-sys-rs-ac621d6b021afa56/dep-lib-cairo_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
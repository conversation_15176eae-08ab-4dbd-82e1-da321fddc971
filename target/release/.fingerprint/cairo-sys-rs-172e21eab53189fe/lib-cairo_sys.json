{"rustc": 11410426090777951712, "features": "[\"glib\", \"use_glib\"]", "declared_features": "[\"freetype\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"v1_18\", \"win32-surface\", \"winapi\", \"x11\", \"xcb\", \"xlib\"]", "target": 12604004911878344227, "profile": 2040997289075261528, "path": 9413903390968691562, "deps": [[4684437522915235464, "libc", false, 7456781002432991227], [6885242093860886281, "build_script_build", false, 6194481629695194364], [13626264195287554611, "glib", false, 4220310707485019487]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cairo-sys-rs-172e21eab53189fe/dep-lib-cairo_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 14113089254465536004, "profile": 1369601567987815722, "path": 8578742989466864104, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-fe81bc98f63f230a/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
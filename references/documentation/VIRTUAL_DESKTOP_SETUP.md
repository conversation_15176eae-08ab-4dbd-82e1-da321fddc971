# Virtual Desktop Setup Documentation

This document provides comprehensive documentation for the virtual desktop (vdesk) system implementation in this Hyprland configuration.

## Overview

The virtual desktop system provides 5 named virtual desktops with Waybar integration for visual feedback and click interaction. The system uses Hyprland's `vdesk` dispatcher for virtual desktop management.

### Virtual Desktop Names
1. **Code** - Development and coding activities
2. **Web** - Web browsing and research
3. **Design** - Graphics, design, and creative work
4. **Chat** - Communication and messaging
5. **Media** - Video, audio, and media consumption

## Configuration Files

### Primary Configuration Files
- `config/hypr/conf/binds.conf` - Contains all virtual desktop keybindings
- `config/hypr/conf/layouts.conf` - Workspace cycling configuration
- `config/waybar/scripts/virtual-desktop.sh` - Waybar module script
- `config/hypr/scripts/waybar-vdesk-monitor.sh` - IPC event monitor
- `home/systemd/.config/systemd/user/waybar-vdesk-monitor.service` - Systemd service

## Keybindings

### Primary Virtual Desktop Navigation
| Keybind | Action | Description |
|---------|---------|-------------|
| `Super + 1` | `vdesk 1` | Switch to Code desktop |
| `Super + 2` | `vdesk 2` | Switch to Web desktop |
| `Super + 3` | `vdesk 3` | Switch to Design desktop |
| `Super + 4` | `vdesk 4` | Switch to Chat desktop |
| `Super + 5` | `vdesk 5` | Switch to Media desktop |

### Window Management
| Keybind | Action | Description |
|---------|---------|-------------|
| `Super + Shift + 1` | `movetodesk 1` | Move active window to Code desktop |
| `Super + Shift + 2` | `movetodesk 2` | Move active window to Web desktop |
| `Super + Shift + 3` | `movetodesk 3` | Move active window to Design desktop |
| `Super + Shift + 4` | `movetodesk 4` | Move active window to Chat desktop |
| `Super + Shift + 5` | `movetodesk 5` | Move active window to Media desktop |

### Cycling Through Desktops
| Keybind | Action | Description |
|---------|---------|-------------|
| `Super + Tab` | `cyclevdesks` | Cycle through populated virtual desktops |
| `Super + Shift + Tab` | `backcyclevdesks` | Cycle through populated virtual desktops (reverse) |

### Advanced Window Management
| Keybind | Action | Description |
|---------|---------|-------------|
| `Super + Ctrl + 1-9,0` | `moveTo.sh X` | Move all windows to workspace X |
| `Super + Ctrl + Down` | `workspace empty` | Open next empty workspace |
| `Super + Mouse Up/Down` | `workspace e±1` | Navigate workspaces with mouse wheel |

## Waybar Integration

### Module Configuration
The virtual desktop system integrates with Waybar through individual modules for each desktop:

```json
"custom/vdesk1": {
    "exec": "~/.config/waybar/scripts/virtual-desktop.sh 1",
    "on-click": "~/.config/waybar/scripts/virtual-desktop.sh 1 click",
    "signal": 8,
    "interval": "once",
    "tooltip": false
}
```

### Visual States
- **Focused Desktop**: `vdesk-focused` class - Shows current active desktop
- **Populated Desktop**: `vdesk-unfocused` class - Shows desktops with windows
- **Empty Desktop**: Hidden - Empty desktops are not displayed

### Real-time Updates
The system uses SIGRTMIN+8 signal for real-time Waybar updates when virtual desktops change.

## IPC Event Monitoring

### Monitor Service
The `waybar-vdesk-monitor.service` systemd service runs continuously to listen for Hyprland IPC events and trigger Waybar updates.

**Service Features:**
- Automatic restart on failure
- Dependency binding to waybar.service
- Security restrictions (NoNewPrivileges, PrivateTmp, etc.)
- Comprehensive logging to systemd journal

### Event Handling
The monitor script (`waybar-vdesk-monitor.sh`) listens to Hyprland's IPC socket for `vdesk>>` events and triggers Waybar updates via SIGRTMIN+8.

**Key Functions:**
- `check_hyprland()` - Verifies Hyprland is running
- `check_waybar()` - Verifies Waybar is running
- `trigger_waybar_update()` - Sends signal to update Waybar modules
- `monitor_vdesk_events()` - Main event monitoring loop

## Layout Configuration

### Workspace Cycling
Located in `config/hypr/conf/layouts.conf`:
```
binds {
  workspace_back_and_forth = true
  allow_workspace_cycles = true
  pass_mouse_when_bound = false
}
```

- `workspace_back_and_forth` - Enables toggling between current and previous workspace
- `allow_workspace_cycles` - Allows cycling through workspaces
- `pass_mouse_when_bound` - Controls mouse event handling when keybinds are active

## Troubleshooting

### Common Issues

1. **Waybar not updating virtual desktop status**
   - Check if `waybar-vdesk-monitor.service` is running:
     ```bash
     systemctl --user status waybar-vdesk-monitor
     ```
   - Restart the service:
     ```bash
     systemctl --user restart waybar-vdesk-monitor
     ```

2. **Virtual desktop clicks not working**
   - Verify the virtual-desktop.sh script is executable
   - Check Waybar configuration for correct signal number (8)

3. **Service not starting automatically**
   - Enable the systemd service:
     ```bash
     systemctl --user enable waybar-vdesk-monitor
     ```
   - Check service dependencies and environment variables

### Log Analysis
View service logs for debugging:
```bash
journalctl --user -u waybar-vdesk-monitor -f
```

### Manual Testing
Test virtual desktop switching manually:
```bash
# Switch to virtual desktop 1
hyprctl dispatch vdesk 1

# Check current virtual desktop status
hyprctl printstate

# Trigger manual Waybar update
pkill -SIGRTMIN+8 waybar
```

## Service Management

### Enable/Disable Service
```bash
# Enable automatic startup
systemctl --user enable waybar-vdesk-monitor

# Start service immediately
systemctl --user start waybar-vdesk-monitor

# Stop service
systemctl --user stop waybar-vdesk-monitor

# Disable automatic startup
systemctl --user disable waybar-vdesk-monitor
```

### Service Status
```bash
# Check service status
systemctl --user status waybar-vdesk-monitor

# View recent logs
journalctl --user -u waybar-vdesk-monitor --since "1 hour ago"
```

## Dependencies

### Required Components
- Hyprland window manager
- Waybar status bar
- socat (for IPC socket communication)
- systemd (for service management)

### Optional Components
- hyprctl (Hyprland control utility - usually bundled)
- pgrep/pkill (process management - standard on most systems)

## File Locations

```
dotfiles/
├── config/hypr/conf/binds.conf              # Virtual desktop keybindings
├── config/hypr/conf/layouts.conf            # Workspace cycling config
├── config/waybar/scripts/virtual-desktop.sh # Waybar module script
├── config/hypr/scripts/waybar-vdesk-monitor.sh # IPC event monitor
└── home/systemd/.config/systemd/user/
    └── waybar-vdesk-monitor.service          # Systemd service definition
```

## Customization

### Adding More Virtual Desktops
1. Update the `VDESK_NAMES` array in `virtual-desktop.sh`
2. Add corresponding keybindings in `binds.conf`
3. Add new Waybar modules in Waybar configuration
4. Update regex pattern in script if needed

### Changing Virtual Desktop Names
Modify the `VDESK_NAMES` array in `config/waybar/scripts/virtual-desktop.sh`:
```bash
declare -A VDESK_NAMES=(
    [1]="Development"
    [2]="Browser"
    [3]="Graphics"
    [4]="Communication"
    [5]="Entertainment"
)
```

### Styling Virtual Desktop Buttons
CSS classes available for customization in Waybar stylesheet:
- `.vdesk-focused` - Currently active virtual desktop
- `.vdesk-unfocused` - Populated but inactive virtual desktop
- `.hidden` - Empty virtual desktops (hidden by default)
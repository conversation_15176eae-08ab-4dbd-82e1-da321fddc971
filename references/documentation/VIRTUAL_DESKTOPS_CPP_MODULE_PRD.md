# Product Requirements Document: Waybar Virtual Desktops CFFI Module

**Version:** 2.0  
**Date:** July 2025  
**Author:** AI Assistant  

---

## Executive Summary

This PRD outlines the development of a native CFFI Waybar module for virtual desktop integration to replace the current shell script-based system. The module will provide real-time, performance-optimized virtual desktop management through Waybar's CFFI interface, eliminating the need for external systemd services and shell scripts.

---

## Problem Statement

### Current Implementation Limitations
- **Performance overhead**: Multiple shell script executions per update
- **System complexity**: Requires external systemd service for monitoring
- **Maintenance burden**: Separate monitoring service + individual scripts
- **Reliability concerns**: Shell script parsing fragility
- **Resource usage**: Continuous background processes

### User Pain Points
- Setup complexity (systemd service + multiple scripts)
- Potential race conditions between monitor and scripts
- Limited integration with Waybar's native features
- Difficult distribution and installation process

---

## Requirements

### Functional Requirements

#### Core Functionality
- **FR-1**: Single unified module displaying all virtual desktops
- **FR-2**: Real-time state updates via Hy<PERSON>rland IPC events
- **FR-3**: Visual differentiation between focused/unfocused/populated states
- **FR-4**: Click handling for virtual desktop switching
- **FR-5**: Dynamic virtual desktop name and icon display
- **FR-6**: Show/hide virtual desktops based on population status

#### Advanced Features
- **FR-7**: Configurable display format and sorting
- **FR-8**: Tooltip support showing virtual desktop details
- **FR-9**: Window count display per virtual desktop
- **FR-10**: Scroll actions for virtual desktop navigation
- **FR-11**: Custom icons per virtual desktop
- **FR-12**: Integration with Waybar's signal system

### Non-Functional Requirements

#### Performance
- **NFR-1**: Sub-10ms response time for virtual desktop switches
- **NFR-2**: <1MB memory footprint for CFFI module
- **NFR-3**: Zero CPU usage when idle
- **NFR-4**: Efficient IPC event handling with async I/O

#### Reliability
- **NFR-5**: Automatic reconnection on Hyprland restart
- **NFR-6**: Graceful degradation when virtual desktop plugin unavailable
- **NFR-7**: Error handling for malformed IPC events
- **NFR-8**: Memory safety (if using Rust implementation)

#### Maintainability
- **NFR-9**: Simple CFFI interface for easy integration
- **NFR-10**: Comprehensive unit test coverage
- **NFR-11**: Clear separation between IPC handling and display logic
- **NFR-12**: Easy installation and distribution

---

## Technical Architecture

### CFFI Interface Design

The module uses Waybar's CFFI interface, exposing a simple C API that Waybar can dynamically load.

#### Core CFFI Interface
```c
typedef struct {
    char* text;           // Main display text
    char* tooltip;        // Hover tooltip
    char* class_;         // CSS class for styling  
    char* percentage;     // Optional percentage value
} waybar_cffi_output_t;

typedef struct {
    char* config_json;    // Module configuration as JSON string
} waybar_cffi_config_t;

// Required CFFI functions
waybar_cffi_output_t* waybar_cffi_init(waybar_cffi_config_t* config);
waybar_cffi_output_t* waybar_cffi_update();
void waybar_cffi_handle_click(int button, int x, int y);
void waybar_cffi_handle_scroll(int direction);
void waybar_cffi_cleanup();
```

### Implementation Architecture (Rust Recommended)

#### Core Components
```rust
pub struct VirtualDesktopsModule {
    config: ModuleConfig,
    hyprland_ipc: HyprlandIPC,
    virtual_desktops: Vec<VirtualDesktop>,
    active_desktop: Option<String>,
}

pub struct VirtualDesktop {
    id: String,
    name: String,
    focused: bool,
    populated: bool,
    window_count: u32,
}

pub struct HyprlandIPC {
    socket_path: PathBuf,
    event_listener: Option<UnixStream>,
}
```

#### IPC Event Handling
```rust
impl HyprlandIPC {
    async fn listen_for_events(&mut self) -> Result<String, Error> {
        // Listen to Hyprland IPC socket
        // Filter for vdesk>> events
        // Return parsed events
    }
    
    async fn get_virtual_desktop_state(&self) -> Result<Vec<VirtualDesktop>, Error> {
        // Execute: hyprctl printstate
        // Parse JSON response
        // Return structured virtual desktop data
    }
}
```

#### Display Logic
```rust
impl VirtualDesktopsModule {
    fn generate_display_text(&self) -> String {
        // Format virtual desktops according to config
        // Apply icons, spacing, and filtering
        // Return formatted display string
    }
    
    fn handle_desktop_click(&mut self, x: i32) -> Result<(), Error> {
        // Determine which virtual desktop was clicked based on position
        // Execute: hyprctl dispatch vdesk N
        // Trigger update
    }
}
```

---

## Configuration Specification

### Module Configuration
```json
"custom/virtual-desktops": {
    "exec": "/path/to/virtual-desktops-cffi.so",
    "return-type": "json",
    "format": "{}",
    "interval": "once",
    "signal": 8,
    "config": {
        "format": "{icon} {name}",
        "format-icons": {
            "1": "󰊤",
            "2": "󰖟", 
            "3": "󰎄"
        },
        "show-empty": false,
        "show-window-count": false,
        "separator": " ",
        "sort-by": "number"
    }
}
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `format` | string | `"{name}"` | Display format: `{name}`, `{icon}`, `{icon} {name}` |
| `format-icons` | object | `{}` | Icon mapping for virtual desktop IDs |
| `show-empty` | boolean | `false` | Show unpopulated virtual desktops |
| `show-window-count` | boolean | `false` | Display window count in tooltip |
| `separator` | string | `" "` | Separator between virtual desktop elements |
| `sort-by` | string | `"number"` | Sort method: "number", "name", "focused-first" |

### CSS Classes
```css
/* Module container */
#virtual-desktops { }

/* State-based styling */
#virtual-desktops.focused { }
#virtual-desktops.populated { }
#virtual-desktops.empty { }

/* Custom class from CFFI module */
#virtual-desktops.vdesk-state { }
```

### CFFI Output Format
```json
{
    "text": "󰊤 Focus  󰖟 Research  󰎄 Design",
    "tooltip": "Virtual Desktops:\n• Focus (2 windows) - focused\n• Research (1 window)\n• Design (0 windows)",
    "class": "vdesk-state populated",
    "percentage": ""
}
```

---

## Implementation Plan

### Phase 1: Core CFFI Module (MVP)
**Duration**: 2-3 hours
- [ ] CFFI interface implementation with basic functions
- [ ] Adapt existing shell script logic to Rust/C
- [ ] Virtual desktop state parsing and display  
- [ ] Basic configuration parsing

### Phase 2: Enhanced Features  
**Duration**: 1-2 hours
- [ ] Click handling for virtual desktop switching
- [ ] Tooltip generation with detailed information
- [ ] Advanced display formatting and icons
- [ ] Error handling and reconnection logic

### Phase 3: Polish & Distribution
**Duration**: 1 hour
- [ ] Testing with current setup
- [ ] Documentation and installation instructions
- [ ] Simple build script or Makefile

### Development Workflow
1. **Setup**: Create standalone project repository
2. **Implementation**: Rust/C CFFI shared library
3. **Testing**: Unit tests + integration testing with Waybar
4. **Packaging**: Cross-platform distribution packages
5. **Documentation**: Usage guide and examples
6. **Release**: GitHub releases with precompiled binaries

---

## Technical Specifications

### Dependencies
- **Runtime**: Hyprland virtual-desktops plugin, Waybar with CFFI support
- **Build**: Rust toolchain or C compiler, no Waybar source required
- **System**: Unix domain sockets, JSON parsing library

### Project Structure (Rust Implementation)
```
waybar-virtual-desktops-cffi/
├── Cargo.toml
├── src/
│   ├── lib.rs              # CFFI interface
│   ├── vdesk.rs            # Virtual desktop logic
│   ├── hyprland.rs         # IPC communication
│   └── config.rs           # Configuration handling
├── tests/
│   ├── integration.rs      # Integration tests
│   └── unit.rs             # Unit tests
├── examples/
│   └── waybar-config.json  # Example Waybar configuration
└── README.md
```

### Build Configuration
```toml
[package]
name = "waybar-virtual-desktops-cffi"
version = "1.0.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["net", "rt"] }
libc = "0.2"
```

### Installation
```bash
# Build from source
cargo build --release

# Install to system
sudo cp target/release/libwaybar_virtual_desktops_cffi.so /usr/lib/waybar/modules/

# Or use package manager
yay -S waybar-virtual-desktops-cffi  # AUR
```

---

## Testing Strategy

### Unit Tests
- Virtual desktop creation/destruction
- IPC event parsing
- State synchronization
- Configuration parsing

### Integration Tests  
- Hyprland plugin compatibility
- Multi-monitor scenarios
- Virtual desktop switching
- Error recovery

### Performance Tests
- Memory usage profiling
- IPC event latency
- Update frequency impact
- CPU usage monitoring

---

## Migration Strategy

### Backwards Compatibility
- Maintain shell script fallback during transition
- Configuration migration guide
- Side-by-side operation support

### Migration Steps
1. **CFFI module installation**: Install alongside existing system
2. **Configuration update**: Switch from multiple modules to single CFFI module
3. **Service cleanup**: Disable old systemd monitoring service
4. **Testing phase**: Verify functionality and performance
5. **Cleanup**: Remove old shell scripts and systemd service

---

## Success Metrics

### Performance Goals
- **Response time**: <5ms virtual desktop switches
- **Memory usage**: <1MB CFFI module footprint  
- **CPU usage**: <0.05% when idle
- **Startup time**: <50ms module initialization

### User Experience Goals
- **Setup time**: <2 minutes from installation to working
- **Reliability**: 99.9% uptime with Hyprland
- **Feature parity**: 100% current functionality preserved + enhanced features
- **Simplicity**: Single file installation, no systemd services

---

## Risk Assessment

### Technical Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Hyprland IPC changes | High | Medium | Version compatibility matrix |
| CFFI interface changes | Medium | Low | Use stable CFFI API subset |
| Performance regression | Low | Low | Comprehensive benchmarking |
| Memory leaks | Medium | Low | Rust memory safety / careful C management |

### Project Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Limited Waybar CFFI adoption | Medium | Low | CFFI is officially supported |
| Maintenance burden | Low | Low | Simple, focused codebase |
| User adoption | Low | Low | Clear migration benefits |
| Distribution complexity | Medium | Low | Multiple package formats |

---

## Future Enhancements

### Potential Features
- **Rich tooltips**: Window previews and application lists
- **Custom animations**: Smooth transitions in display text
- **Multi-monitor support**: Per-monitor virtual desktop tracking
- **Workspace rules integration**: Automatic application placement
- **Theme synchronization**: Auto-update colors from wallpaper

### Extension Points
- Configuration hot-reloading without restart
- Plugin system for custom formatters
- Integration with other Hyprland plugins
- Export metrics for system monitoring

### CFFI Module Ecosystem
This CFFI implementation could serve as a template for other Hyprland plugin integrations:
- Window rules module
- Animation controls module  
- Plugin manager module

---

This PRD provides a comprehensive roadmap for implementing a CFFI-based Waybar virtual desktops module that will significantly improve performance, reliability, and user experience over the current shell script approach. The CFFI approach offers the benefits of native performance while maintaining simplicity and independence from Waybar's internal development cycle.
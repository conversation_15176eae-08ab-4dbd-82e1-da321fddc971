# Taken from Hyprland repo
name: "Setup base"

inputs:
  INSTALL_XORG_PKGS:
    description: 'Install xorg dependencies'
    required: false
    default: false
  branch:
    description: 'Hyprland branch to checkout'
    required: false
    default: 'main'
  hyprwayland:
    description: 'Hyprwayland branch to checkout'
    required: false
    default: 'main'

runs:
  using: "composite"
  steps:
    - name: Get required pacman pkgs
      shell: bash
      run: |
        sed -i 's/SigLevel    = Required DatabaseOptional/SigLevel    = Optional TrustAll/' /etc/pacman.conf
        pacman --noconfirm --noprogressbar -Syyu
        pacman --noconfirm --noprogressbar -Sy \
          base-devel \
          cairo \
          clang \
          cmake \
          cpio \
          git \
          glm \
          glslang \
          go \
          hyprlang \
          hyprcursor \
          hyprutils \
          jq \
          libc++ \
          libdisplay-info \
          libdrm \
          libepoxy \
          libfontenc \
          libglvnd \
          libinput \
          libliftoff \
          libxcvt \
          libxfont2 \
          libxkbcommon \
          libxkbfile \
          libxcursor \
          lld \
          meson \
          ninja \
          pango \
          pixman \
          pkgconf \
          pugixml \
          scdoc \
          seatd \
          systemd \
          tomlplusplus \
          wayland \
          wayland-protocols \
          xcb-util-errors \
          xcb-util-renderutil \
          xcb-util-wm \
          xcb-util \
          xcb-util-image \
          libzip \
          librsvg \
          libwebp \
          libspng \
          libjxl \
          libjpeg-turbo \
          re2

    - name: Get hyprwayland-scanner-git
      shell: bash
      run: |
        git clone https://github.com/hyprwm/hyprwayland-scanner --recursive
        cd hyprwayland-scanner
        git checkout ${{ inputs.hyprwayland }} && git submodule update --recursive --init
        cmake --no-warn-unused-cli -DCMAKE_BUILD_TYPE:STRING=Release -DCMAKE_INSTALL_PREFIX:PATH=/usr -S . -B ./build
        cmake --build ./build --config Release --target all -j`nproc 2>/dev/null || getconf NPROCESSORS_CONF`
        cmake --install build

    - name: Get hyprutils
      shell: bash
      run: |
        git clone https://github.com/hyprwm/hyprutils.git
        cd hyprutils
        cmake --no-warn-unused-cli -DCMAKE_BUILD_TYPE:STRING=Release -DCMAKE_INSTALL_PREFIX:PATH=/usr -S . -B ./build
        cmake --build ./build --config Release --target all -j`nproc 2>/dev/null || getconf _NPROCESSORS_CONF`
        cmake --install build

    - name: Get aquamarine
      shell: bash
      run: |
        git clone https://github.com/hyprwm/aquamarine.git
        cd aquamarine
        cmake --no-warn-unused-cli -DCMAKE_BUILD_TYPE:STRING=Release -DCMAKE_INSTALL_PREFIX:PATH=/usr -S . -B ./build
        cmake --build ./build --config Release --target all -j`nproc 2>/dev/null || getconf _NPROCESSORS_CONF`
        cmake --install build

    - name: Get hyprgraphics
      shell: bash
      run: |
        git clone https://github.com/hyprwm/hyprgraphics.git
        cd hyprgraphics
        cmake --no-warn-unused-cli -DCMAKE_BUILD_TYPE:STRING=Release -DCMAKE_INSTALL_PREFIX:PATH=/usr -S . -B ./build
        cmake --build ./build --config Release --target all -j`nproc 2>/dev/null || getconf _NPROCESSORS_CONF`
        cmake --install build

    - name: Get Xorg pacman pkgs
      shell: bash
      if: inputs.INSTALL_XORG_PKGS == 'true'
      run: |
        pacman --noconfirm --noprogressbar -Sy \
          xorg-fonts-encodings \
          xorg-server-common \
          xorg-setxkbmap \
          xorg-xkbcomp \
          xorg-xwayland

    - name: Get Hyprland
      shell: bash
      run: |
        git clone https://github.com/hyprwm/Hyprland.git --recursive
        cd Hyprland
        git checkout ${{ inputs.branch }} && git submodule update --recursive --init

    # Fix an issue with actions/checkout where the checkout repo is not mark as safe
    - name: Mark directory as safe for git
      shell: bash
      run: |
        git config --global --add safe.directory /__w/Hyprland/Hyprland

[{"directory": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build", "command": "/usr/bin/g++ -DDEBUG -Dhyprland_virtual_desktops_EXPORTS -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique -o CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDesk.cpp.o -c /home/<USER>/dotfiles/hyprland-virtual-desktops/src/VirtualDesk.cpp", "file": "/home/<USER>/dotfiles/hyprland-virtual-desktops/src/VirtualDesk.cpp", "output": "CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDesk.cpp.o"}, {"directory": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build", "command": "/usr/bin/g++ -DDEBUG -Dhyprland_virtual_desktops_EXPORTS -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique -o CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDeskManager.cpp.o -c /home/<USER>/dotfiles/hyprland-virtual-desktops/src/VirtualDeskManager.cpp", "file": "/home/<USER>/dotfiles/hyprland-virtual-desktops/src/VirtualDeskManager.cpp", "output": "CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDeskManager.cpp.o"}, {"directory": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build", "command": "/usr/bin/g++ -DDEBUG -Dhyprland_virtual_desktops_EXPORTS -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique -o CMakeFiles/hyprland-virtual-desktops.dir/src/main.cpp.o -c /home/<USER>/dotfiles/hyprland-virtual-desktops/src/main.cpp", "file": "/home/<USER>/dotfiles/hyprland-virtual-desktops/src/main.cpp", "output": "CMakeFiles/hyprland-virtual-desktops.dir/src/main.cpp.o"}, {"directory": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build", "command": "/usr/bin/g++ -DDEBUG -Dhyprland_virtual_desktops_EXPORTS -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique -o CMakeFiles/hyprland-virtual-desktops.dir/src/sticky_apps.cpp.o -c /home/<USER>/dotfiles/hyprland-virtual-desktops/src/sticky_apps.cpp", "file": "/home/<USER>/dotfiles/hyprland-virtual-desktops/src/sticky_apps.cpp", "output": "CMakeFiles/hyprland-virtual-desktops.dir/src/sticky_apps.cpp.o"}, {"directory": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build", "command": "/usr/bin/g++ -DDEBUG -Dhyprland_virtual_desktops_EXPORTS -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique -o CMakeFiles/hyprland-virtual-desktops.dir/src/utils.cpp.o -c /home/<USER>/dotfiles/hyprland-virtual-desktops/src/utils.cpp", "file": "/home/<USER>/dotfiles/hyprland-virtual-desktops/src/utils.cpp", "output": "CMakeFiles/hyprland-virtual-desktops.dir/src/utils.cpp.o"}]
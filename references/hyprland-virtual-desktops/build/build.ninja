# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: hyprland-virtual-desktops
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/dotfiles/hyprland-virtual-desktops/build/
# =============================================================================
# Object build statements for SHARED_LIBRARY target hyprland-virtual-desktops


#############################################
# Order-only phony target for hyprland-virtual-desktops

build cmake_object_order_depends_target_hyprland-virtual-desktops: phony || .

build CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDesk.cpp.o: CXX_COMPILER__hyprland-virtual-desktops_unscanned_Debug /home/<USER>/dotfiles/hyprland-virtual-desktops/src/VirtualDesk.cpp || cmake_object_order_depends_target_hyprland-virtual-desktops
  CONFIG = Debug
  DEFINES = -DDEBUG -Dhyprland_virtual_desktops_EXPORTS
  DEP_FILE = CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDesk.cpp.o.d
  FLAGS = -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique
  INCLUDES = -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include
  OBJECT_DIR = CMakeFiles/hyprland-virtual-desktops.dir
  OBJECT_FILE_DIR = CMakeFiles/hyprland-virtual-desktops.dir/src

build CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDeskManager.cpp.o: CXX_COMPILER__hyprland-virtual-desktops_unscanned_Debug /home/<USER>/dotfiles/hyprland-virtual-desktops/src/VirtualDeskManager.cpp || cmake_object_order_depends_target_hyprland-virtual-desktops
  CONFIG = Debug
  DEFINES = -DDEBUG -Dhyprland_virtual_desktops_EXPORTS
  DEP_FILE = CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDeskManager.cpp.o.d
  FLAGS = -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique
  INCLUDES = -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include
  OBJECT_DIR = CMakeFiles/hyprland-virtual-desktops.dir
  OBJECT_FILE_DIR = CMakeFiles/hyprland-virtual-desktops.dir/src

build CMakeFiles/hyprland-virtual-desktops.dir/src/main.cpp.o: CXX_COMPILER__hyprland-virtual-desktops_unscanned_Debug /home/<USER>/dotfiles/hyprland-virtual-desktops/src/main.cpp || cmake_object_order_depends_target_hyprland-virtual-desktops
  CONFIG = Debug
  DEFINES = -DDEBUG -Dhyprland_virtual_desktops_EXPORTS
  DEP_FILE = CMakeFiles/hyprland-virtual-desktops.dir/src/main.cpp.o.d
  FLAGS = -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique
  INCLUDES = -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include
  OBJECT_DIR = CMakeFiles/hyprland-virtual-desktops.dir
  OBJECT_FILE_DIR = CMakeFiles/hyprland-virtual-desktops.dir/src

build CMakeFiles/hyprland-virtual-desktops.dir/src/sticky_apps.cpp.o: CXX_COMPILER__hyprland-virtual-desktops_unscanned_Debug /home/<USER>/dotfiles/hyprland-virtual-desktops/src/sticky_apps.cpp || cmake_object_order_depends_target_hyprland-virtual-desktops
  CONFIG = Debug
  DEFINES = -DDEBUG -Dhyprland_virtual_desktops_EXPORTS
  DEP_FILE = CMakeFiles/hyprland-virtual-desktops.dir/src/sticky_apps.cpp.o.d
  FLAGS = -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique
  INCLUDES = -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include
  OBJECT_DIR = CMakeFiles/hyprland-virtual-desktops.dir
  OBJECT_FILE_DIR = CMakeFiles/hyprland-virtual-desktops.dir/src

build CMakeFiles/hyprland-virtual-desktops.dir/src/utils.cpp.o: CXX_COMPILER__hyprland-virtual-desktops_unscanned_Debug /home/<USER>/dotfiles/hyprland-virtual-desktops/src/utils.cpp || cmake_object_order_depends_target_hyprland-virtual-desktops
  CONFIG = Debug
  DEFINES = -DDEBUG -Dhyprland_virtual_desktops_EXPORTS
  DEP_FILE = CMakeFiles/hyprland-virtual-desktops.dir/src/utils.cpp.o.d
  FLAGS = -g -std=gnu++23 -fPIC -Wall -fPIC --no-gnu-unique
  INCLUDES = -I/home/<USER>/dotfiles/hyprland-virtual-desktops/include -isystem /var/cache/hyprpm/givanib/headersRoot/include -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols -isystem /var/cache/hyprpm/givanib/headersRoot/include/hyprland -isystem /usr/include/libdrm -isystem /usr/include/pixman-1 -isystem /usr/include/c++/15.1.1 -isystem /usr/include/c++/15.1.1/x86_64-pc-linux-gnu -isystem /usr/include/c++/15.1.1/backward -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include -isystem /usr/local/include -isystem /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed -isystem /usr/include
  OBJECT_DIR = CMakeFiles/hyprland-virtual-desktops.dir
  OBJECT_FILE_DIR = CMakeFiles/hyprland-virtual-desktops.dir/src


# =============================================================================
# Link build statements for SHARED_LIBRARY target hyprland-virtual-desktops


#############################################
# Link the shared library libhyprland-virtual-desktops.so

build libhyprland-virtual-desktops.so: CXX_SHARED_LIBRARY_LINKER__hyprland-virtual-desktops_Debug CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDesk.cpp.o CMakeFiles/hyprland-virtual-desktops.dir/src/VirtualDeskManager.cpp.o CMakeFiles/hyprland-virtual-desktops.dir/src/main.cpp.o CMakeFiles/hyprland-virtual-desktops.dir/src/sticky_apps.cpp.o CMakeFiles/hyprland-virtual-desktops.dir/src/utils.cpp.o | /usr/lib/libdrm.so /usr/lib/libpixman-1.so
  CONFIG = Debug
  DEP_FILE = CMakeFiles/hyprland-virtual-desktops.dir/link.d
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -Wl,--dependency-file=CMakeFiles/hyprland-virtual-desktops.dir/link.d
  LINK_LIBRARIES = -lrt  /usr/lib/libdrm.so  /usr/lib/libpixman-1.so
  OBJECT_DIR = CMakeFiles/hyprland-virtual-desktops.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libhyprland-virtual-desktops.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = libhyprland-virtual-desktops.so
  TARGET_PDB = hyprland-virtual-desktops.so.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dotfiles/hyprland-virtual-desktops/build && /usr/bin/ccmake -S/home/<USER>/dotfiles/hyprland-virtual-desktops -B/home/<USER>/dotfiles/hyprland-virtual-desktops/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dotfiles/hyprland-virtual-desktops/build && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/dotfiles/hyprland-virtual-desktops -B/home/<USER>/dotfiles/hyprland-virtual-desktops/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/dotfiles/hyprland-virtual-desktops/build && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/dotfiles/hyprland-virtual-desktops/build && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/dotfiles/hyprland-virtual-desktops/build && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build hyprland-virtual-desktops: phony libhyprland-virtual-desktops.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/dotfiles/hyprland-virtual-desktops/build

build all: phony libhyprland-virtual-desktops.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /home/<USER>/dotfiles/hyprland-virtual-desktops/build/cmake_install.cmake: RERUN_CMAKE | /home/<USER>/dotfiles/hyprland-virtual-desktops/CMakeLists.txt /usr/share/cmake/Modules/CMakeCInformation.cmake /usr/share/cmake/Modules/CMakeCXXInformation.cmake /usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake/Modules/CMakeGenericSystem.cmake /usr/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake/Modules/Compiler/GNU-C.cmake /usr/share/cmake/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake/Modules/Compiler/GNU.cmake /usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake/Modules/FindPackageMessage.cmake /usr/share/cmake/Modules/FindPkgConfig.cmake /usr/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/share/cmake/Modules/Linker/GNU-C.cmake /usr/share/cmake/Modules/Linker/GNU-CXX.cmake /usr/share/cmake/Modules/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU-C.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake/Modules/Platform/Linux.cmake /usr/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.3-dirty/CMakeCCompiler.cmake CMakeFiles/4.0.3-dirty/CMakeCXXCompiler.cmake CMakeFiles/4.0.3-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/dotfiles/hyprland-virtual-desktops/CMakeLists.txt /usr/share/cmake/Modules/CMakeCInformation.cmake /usr/share/cmake/Modules/CMakeCXXInformation.cmake /usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake/Modules/CMakeGenericSystem.cmake /usr/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake/Modules/Compiler/GNU-C.cmake /usr/share/cmake/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake/Modules/Compiler/GNU.cmake /usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake/Modules/FindPackageMessage.cmake /usr/share/cmake/Modules/FindPkgConfig.cmake /usr/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/share/cmake/Modules/Linker/GNU-C.cmake /usr/share/cmake/Modules/Linker/GNU-CXX.cmake /usr/share/cmake/Modules/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU-C.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake/Modules/Platform/Linux.cmake /usr/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.3-dirty/CMakeCCompiler.cmake CMakeFiles/4.0.3-dirty/CMakeCXXCompiler.cmake CMakeFiles/4.0.3-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all

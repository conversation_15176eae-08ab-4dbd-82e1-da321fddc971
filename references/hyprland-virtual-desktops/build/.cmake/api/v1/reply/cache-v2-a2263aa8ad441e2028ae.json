{"entries": [{"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/addr2line"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "0"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/usr/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/usr/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/usr/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/g++"}, {"name": "CMAKE_CXX_COMPILER_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ar"}, {"name": "CMAKE_CXX_COMPILER_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ranlib"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/gcc"}, {"name": "CMAKE_C_COMPILER_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ar"}, {"name": "CMAKE_C_COMPILER_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ranlib"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EDIT_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cache edit program executable."}], "type": "INTERNAL", "value": "/usr/bin/ccmake"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "ELF"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_BUILD_DATABASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of build database during the build."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "ON"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/home/<USER>/dotfiles/hyprland-virtual-desktops"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_INSTALL_SO_NO_EXE", "properties": [{"name": "HELPSTRING", "value": "Install .so files without execute permission."}], "type": "INTERNAL", "value": "0"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ld"}, {"name": "CMAKE_LIST_FILE_NAME", "properties": [{"name": "HELPSTRING", "value": "Name of CMakeLists files to read"}], "type": "INTERNAL", "value": "CMakeLists.txt"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program used to build from build.ninja files."}], "type": "FILEPATH", "value": "/usr/bin/ninja"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objcopy"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objdump"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "Virtual desktop like workspaces"}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "hyprland-virtual-desktops"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "2.2.7"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "2"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "2"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "7"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/readelf"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/usr/share/cmake"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/strip"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_TAPI-NOTFOUND"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig", "properties": [{"name": "HELPSTRING", "value": "Details about finding PkgConfig"}], "type": "INTERNAL", "value": "[/usr/bin/pkg-config][v2.5.1()]"}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "/usr/bin/pkg-config"}, {"name": "__pkg_config_arguments_deps", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;IMPORTED_TARGET;hyprland;libdrm;pixman-1"}, {"name": "__pkg_config_checked_deps", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "deps_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/var/cache/hyprpm/givanib/headersRoot/include;-I/var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols;-I/var/cache/hyprpm/givanib/headersRoot/include/hyprland;-I/usr/include;-I/usr/include/libdrm;-I/usr/include/pixman-1"}, {"name": "deps_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "deps_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/var/cache/hyprpm/givanib/headersRoot/include;/var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols;/var/cache/hyprpm/givanib/headersRoot/include/hyprland;/usr/include;/usr/include/libdrm;/usr/include/pixman-1"}, {"name": "deps_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib;-ldrm;-lpixman-1"}, {"name": "deps_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "drm;pixman-1"}, {"name": "deps_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "deps_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/var/cache/hyprpm/givanib/headersRoot/include;-I/var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols;-I/var/cache/hyprpm/givanib/headersRoot/include/hyprland;-I/usr/include;-I/usr/include/libdrm;-I/usr/include/pixman-1"}, {"name": "deps_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/var/cache/hyprpm/givanib/headersRoot/include;/var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols;/var/cache/hyprpm/givanib/headersRoot/include/hyprland;/usr/include;/usr/include/libdrm;/usr/include/pixman-1"}, {"name": "deps_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib;-ldrm;-lpixman-1;-lm;-pthread"}, {"name": "deps_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "deps_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "drm;pixman-1;m"}, {"name": "deps_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "deps_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_hyprland_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_hyprland_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "deps_hyprland_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "hyprland"}, {"name": "deps_hyprland_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/var/cache/hyprpm/givanib/headersRoot/include"}, {"name": "deps_hyprland_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "0.49.0"}, {"name": "deps_libdrm_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "deps_libdrm_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "deps_libdrm_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libdrm"}, {"name": "deps_libdrm_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "deps_libdrm_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2.4.125"}, {"name": "deps_pixman-1_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "deps_pixman-1_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "deps_pixman-1_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "pixman-1"}, {"name": "deps_pixman-1_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "deps_pixman-1_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "0.46.2"}, {"name": "hyprland-virtual-desktops_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build"}, {"name": "hyprland-virtual-desktops_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "hyprland-virtual-desktops_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/dotfiles/hyprland-virtual-desktops"}, {"name": "pkgcfg_lib_deps_drm", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/libdrm.so"}, {"name": "pkgcfg_lib_deps_pixman-1", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/libpixman-1.so"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}], "kind": "cache", "version": {"major": 2, "minor": 0}}
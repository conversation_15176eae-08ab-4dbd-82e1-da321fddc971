{"artifacts": [{"path": "libhyprland-virtual-desktops.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "set_property", "_pkg_create_imp_target", "_pkg_recalculate", "pkg_check_modules", "add_compile_options", "add_compile_definitions", "include_directories"], "files": ["CMakeLists.txt", "/usr/share/cmake/Modules/FindPkgConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 1, "file": 0, "line": 43, "parent": 0}, {"command": 2, "file": 0, "line": 41, "parent": 0}, {"command": 6, "file": 0, "line": 36, "parent": 0}, {"command": 5, "file": 1, "line": 877, "parent": 4}, {"command": 4, "file": 1, "line": 361, "parent": 5}, {"command": 3, "file": 1, "line": 342, "parent": 6}, {"command": 7, "file": 0, "line": 16, "parent": 0}, {"command": 8, "file": 0, "line": 24, "parent": 0}, {"command": 9, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++23 -fPIC"}, {"backtrace": 8, "fragment": "-Wall"}, {"backtrace": 8, "fragment": "-fPIC"}, {"backtrace": 8, "fragment": "--no-gnu-unique"}], "defines": [{"backtrace": 9, "define": "DEBUG"}, {"define": "hyprland_virtual_desktops_EXPORTS"}], "includes": [{"backtrace": 10, "path": "/home/<USER>/dotfiles/hyprland-virtual-desktops/include"}, {"backtrace": 3, "isSystem": true, "path": "/var/cache/hyprpm/givanib/headersRoot/include"}, {"backtrace": 3, "isSystem": true, "path": "/var/cache/hyprpm/givanib/headersRoot/include/hyprland/protocols"}, {"backtrace": 3, "isSystem": true, "path": "/var/cache/hyprpm/givanib/headersRoot/include/hyprland"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/libdrm"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/pixman-1"}, {"isSystem": true, "path": "/usr/include/c++/15.1.1"}, {"isSystem": true, "path": "/usr/include/c++/15.1.1/x86_64-pc-linux-gnu"}, {"isSystem": true, "path": "/usr/include/c++/15.1.1/backward"}, {"isSystem": true, "path": "/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include"}, {"isSystem": true, "path": "/usr/local/include"}, {"isSystem": true, "path": "/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed"}, {"isSystem": true, "path": "/usr/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "23"}, "sourceIndexes": [0, 1, 2, 3, 4]}], "id": "hyprland-virtual-desktops::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"backtrace": 3, "fragment": "-lrt", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/libdrm.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/libpixman-1.so", "role": "libraries"}], "language": "CXX"}, "name": "hyprland-virtual-desktops", "nameOnDisk": "libhyprland-virtual-desktops.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/VirtualDesk.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/VirtualDeskManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/sticky_apps.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}
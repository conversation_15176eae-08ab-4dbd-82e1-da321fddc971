{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3-dirty/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linux-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3-dirty/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3-dirty/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Linker/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Linker/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build", "source": "/home/<USER>/dotfiles/hyprland-virtual-desktops"}, "version": {"major": 1, "minor": 1}}
{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake"}, "version": {"isDirty": true, "major": 4, "minor": 0, "patch": 3, "string": "4.0.3-dirty", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-6f65d5a6f7771f04a17e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-a2263aa8ad441e2028ae.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-28113a8a08f6610ed2bf.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-1726135e1824a66542a0.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-a2263aa8ad441e2028ae.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-6f65d5a6f7771f04a17e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-1726135e1824a66542a0.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-28113a8a08f6610ed2bf.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}
{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include", "/usr/local/include", "/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1", "/usr/lib", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["gcc", "gcc_s", "c", "gcc", "gcc_s"]}, "path": "/usr/bin/gcc", "version": "15.1.1"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/include/c++/15.1.1", "/usr/include/c++/15.1.1/x86_64-pc-linux-gnu", "/usr/include/c++/15.1.1/backward", "/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include", "/usr/local/include", "/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include-fixed", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1", "/usr/lib", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "/usr/bin/g++", "version": "15.1.1"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}
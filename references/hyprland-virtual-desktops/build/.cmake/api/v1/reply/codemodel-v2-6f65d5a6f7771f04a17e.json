{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-dfd72e582c1919b62a34.json", "minimumCMakeVersion": {"string": "3.27"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "hyprland-virtual-desktops", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "hyprland-virtual-desktops::@6890427a1f51a3e7e1df", "jsonFile": "target-hyprland-virtual-desktops-Debug-835b5321dcd37f577751.json", "name": "hyprland-virtual-desktops", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/dotfiles/hyprland-virtual-desktops/build", "source": "/home/<USER>/dotfiles/hyprland-virtual-desktops"}, "version": {"major": 2, "minor": 8}}
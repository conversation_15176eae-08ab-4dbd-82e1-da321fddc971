# Plan for hyprland-virtual-desktops Plugin Enhancement

## Goal
To enhance the `cyclevdesks` and `backcyclevdesks` dispatchers in the `hyprland-virtual-desktops` plugin, allowing users to optionally cycle only through virtual desktops that currently contain windows (i.e., "populated" desktops). This behavior will be controlled by a new configuration option.

## Planned Changes and Motivations

1.  **Introduce a new configuration option: `cycle_populated_only`**
    *   **Location:** This will be a new entry within the `plugin:virtual-desktops:` section of your `hyprland.conf` file.
    *   **Type:** It will be an integer, where `0` means "cycle through all virtual desktops" (current behavior) and `1` means "cycle only through populated virtual desktops."
    *   **Default Value:** The default will be `0` (false).
    *   **Motivation:** This provides users with the flexibility to choose their preferred cycling behavior. By defaulting to `0`, we ensure that existing setups are not unexpectedly altered, adhering to the principle of minimal disruption.

2.  **Implement `isDeskPopulated(int vdeskId)` method in `VirtualDeskManager`**
    *   **Location:** This method will be declared in `include/VirtualDeskManager.hpp` and implemented in `src/VirtualDeskManager.cpp`.
    *   **Functionality:** This method will determine if a specific virtual desktop (identified by its `vdeskId`) is "populated." A virtual desktop is considered populated if any of the workspaces associated with its active layout contain at least one window.
    *   **Motivation:** We've identified that the `hyprctl printstate` command already uses logic to determine if a desktop is populated. This new method will encapsulate and reuse that existing, proven logic, ensuring consistency and avoiding redundant code.

3.  **Modify `nextDeskId()` and `prevDeskId()` in `VirtualDeskManager`**
    *   **Location:** These functions are located in `src/VirtualDeskManager.cpp`.
    *   **Functionality:** These methods, responsible for calculating the next or previous virtual desktop ID during cycling, will be updated to:
        *   Read the value of the new `cycle_populated_only` configuration option.
        *   If `cycle_populated_only` is set to `1`, they will use the `isDeskPopulated()` method to skip over any unpopulated virtual desktops, ensuring that the cycling only lands on desktops with active windows.
        *   If `cycle_populated_only` is set to `0`, they will continue to function as they do currently, cycling through all virtual desktops regardless of their population status.
    *   **Motivation:** This is where the core logic for the new feature will be integrated. By modifying these functions, we directly control the behavior of the `cyclevdesks` and `backcyclevdesks` dispatchers based on the user's configuration.

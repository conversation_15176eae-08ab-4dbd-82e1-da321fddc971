pkgbase = hyprland
	pkgdesc = a highly customizable dynamic tiling Wayland compositor
	pkgver = 0.49.0
	pkgrel = 2
	url = https://github.com/hyprwm/Hyprland
	arch = x86_64
	arch = aarch64
	license = BSD-3-Clause
	makedepends = cmake
	makedepends = glaze
	makedepends = hyprland-protocols
	makedepends = meson
	makedepends = ninja
	makedepends = xorgproto
	depends = cairo
	depends = aquamarine
	depends = libaquamarine.so
	depends = gcc-libs
	depends = glibc
	depends = glib2
	depends = libgio-2.0.so
	depends = libgobject-2.0.so
	depends = glslang
	depends = hyprcursor
	depends = libhyprcursor.so
	depends = hyprgraphics
	depends = libhyprgraphics.so
	depends = hyprland-qtutils
	depends = hyprlang
	depends = libhyprlang.so
	depends = hyprutils
	depends = libhyprutils.so
	depends = hyprwayland-scanner
	depends = libdisplay-info
	depends = libdisplay-info.so
	depends = libdrm
	depends = libglvnd
	depends = libEGL.so
	depends = libGLESv2.so
	depends = libOpenGL.so
	depends = libinput
	depends = libliftoff
	depends = libliftoff.so
	depends = libx11
	depends = libxcb
	depends = libxcomposite
	depends = libxcursor
	depends = libxfixes
	depends = libxkbcommon
	depends = libxkbcommon.so
	depends = libxrender
	depends = mesa
	depends = opengl-driver
	depends = pango
	depends = libpango-1.0.so
	depends = libpangocairo-1.0.so
	depends = pixman
	depends = libpixman-1.so
	depends = re2
	depends = libre2.so
	depends = seatd
	depends = libseat.so
	depends = systemd-libs
	depends = libsystemd.so
	depends = tomlplusplus
	depends = libtomlplusplus.so
	depends = libudev.so
	depends = util-linux-libs
	depends = libuuid.so
	depends = wayland
	depends = libwayland-client.so
	depends = libwayland-server.so
	depends = wayland-protocols
	depends = xcb-proto
	depends = xcb-util
	depends = xcb-util-errors
	depends = xcb-util-image
	depends = xcb-util-keysyms
	depends = xcb-util-renderutil
	depends = xcb-util-wm
	depends = xorg-xwayland
	optdepends = cmake: to build and install plugins using hyprpm
	optdepends = cpio: to build and install plugins using hyprpm
	optdepends = glaze: to build and install plugins using hyprpm
	optdepends = hyprland-protocols: to build and install plugins using hyprpm
	optdepends = meson: to build and install plugins using hyprpm
	optdepends = uwsm: the recommended way to start Hyprland
	provides = wayland-compositor
	source = Hyprland-0.49.0.tar.gz::https://github.com/hyprwm/Hyprland/releases/download/v0.49.0/source-v0.49.0.tar.gz
	sha256sums = fd96fb043cfeda09a1ab9a5eb69fee55562475c0c6a41f79dad2bcc652dc5730

pkgname = hyprland

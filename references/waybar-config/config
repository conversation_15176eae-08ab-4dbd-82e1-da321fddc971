{
        "layer": "top",
        "position": "top",
        "reload_style_on_change": true,
        "modules-left": ["custom/notification","clock","custom/pacman","tray"],
        "modules-center": ["custom/vdesk-1", "custom/vdesk-2", "custom/vdesk-3", "custom/vdesk-4", "custom/vdesk-5"],
        "modules-right": ["group/expand","custom/brightness","bluetooth","network","pulseaudio","battery","custom/power-profile","custom/wlogout"],


        "hyprland/workspaces": {
            "format": "{name}",
            "format-icons": {
                "active": "",
                "default": "",
                "empty": ""
            },
            "persistent-workspaces": {
                "*": [ 1,2,3,4,5 ]
            }
        },
        "custom/vdesk-1": {
            "format": "{}",
            "return-type": "json",
            "exec": "~/.config/waybar/scripts/virtual-desktop.sh 1",
            "on-click": "~/.config/waybar/scripts/virtual-desktop.sh 1 click",
            "interval": "once",
            "signal": 8
        },
        "custom/vdesk-2": {
            "format": "{}",
            "return-type": "json",
            "exec": "~/.config/waybar/scripts/virtual-desktop.sh 2",
            "on-click": "~/.config/waybar/scripts/virtual-desktop.sh 2 click",
            "interval": "once",
            "signal": 8
        },
        "custom/vdesk-3": {
            "format": "{}",
            "return-type": "json",
            "exec": "~/.config/waybar/scripts/virtual-desktop.sh 3",
            "on-click": "~/.config/waybar/scripts/virtual-desktop.sh 3 click",
            "interval": "once",
            "signal": 8
        },
        "custom/vdesk-4": {
            "format": "{}",
            "return-type": "json",
            "exec": "~/.config/waybar/scripts/virtual-desktop.sh 4",
            "on-click": "~/.config/waybar/scripts/virtual-desktop.sh 4 click",
            "interval": "once",
            "signal": 8
        },
        "custom/vdesk-5": {
            "format": "{}",
            "return-type": "json",
            "exec": "~/.config/waybar/scripts/virtual-desktop.sh 5",
            "on-click": "~/.config/waybar/scripts/virtual-desktop.sh 5 click",
            "interval": "once",
            "signal": 8
        },
        "custom/notification": {
            "tooltip": false,
            "format": "",
            "on-click": "swaync-client -t -sw",
            "escape": true
        },
        "clock": {
            "format": "{:%I:%M:%S %p} ",
            "interval": 1,   
            "tooltip-format": "<tt>{calendar}</tt>",
            "calendar": {
                "format": {
                    "today": "<span color='#fAfBfC'><b>{}</b></span>"
                }
            },
            "actions": {
                "on-click-right": "shift_down",
                "on-click": "shift_up"
            }
        },
        "network": {
            "format-wifi": "",
            "format-ethernet":"",
            "format-disconnected": "",
            "tooltip-format-disconnected": "Error",
            "tooltip-format-wifi": "{essid} ({signalStrength}%) ",
            "tooltip-format-ethernet": "{ifname} 🖧 ",
            "on-click": "~/.config/waybar/scripts/network-menu.sh"
        },
        "bluetooth": {
            "format-on": "󰂯",
            "format-off": "BT-off",
            "format-disabled": "󰂲",
            "format-connected-battery": "󰂯",
            "format-alt": "{device_alias} 󰂯",
            "tooltip-format": "{controller_alias}\t{controller_address}\n\n{num_connections} connected",
            "tooltip-format-connected": "{controller_alias}\t{controller_address}\n\n{num_connections} connected\n\n{device_enumerate}",
            "tooltip-format-enumerate-connected": "{device_alias}\n{device_address}",
            "tooltip-format-enumerate-connected-battery": "{device_alias}\n{device_address}\n{device_battery_percentage}%",
            "on-click": "~/.config/waybar/scripts/bluetooth-menu.sh",
            "on-click-right": "blueman-manager",
        },
        "pulseaudio": {
            "format": "󰕾 {volume}%",
            "format-bluetooth": "󰕾 {volume}%",
            "format-bluetooth-muted": "󰝟",
            "format-muted": "󰝟",
            "format-source": "{volume}% ",
            "format-source-muted": "",
            "format-icons": {
                "headphone": "",
                "hands-free": "󰂑",
                "headset": "󰂑",
                "phone": "",
                "portable": "",
                "car": "",
                "default": ["", "", ""]
            },
            "on-click": "pwvucontrol",
            "on-click-right": "pamixer --toggle-mute",
            "on-scroll-up": "pamixer --increase 5",
            "on-scroll-down": "pamixer --decrease 5",
            "tooltip-format": "{desc}\nVolume: {volume}%"
        }, 
        "battery": {
            "interval":30,
            "states": {
                "good": 95,
                "warning": 30,
                "critical": 20
            },
            "format": "{icon} {capacity}%",
            "format-charging": "󰂄 {capacity}%",
            "format-plugged": "{capacity}% 󰂄 ",
            "format-alt": "{time} {icon}",
            "format-icons": [
                "󰁻",
            "󰁼",
            "󰁾",
            "󰂀",
            "󰂂",
            "󰁹"
            ],
            "on-click-right": "~/.config/waybar_popups/manager.py battery"
        },
        "custom/power-profile": {
            "format": "{}",
            "return-type": "json",
            "interval": 30,
            "exec": "~/.config/waybar/scripts/power-profile.sh -j",
            "on-click": "~/.config/waybar/scripts/power-profile.sh -c && pkill -SIGRTMIN+10 waybar",
            "on-click-right": "~/.config/waybar/scripts/power-profile.sh -r && pkill -SIGRTMIN+10 waybar",
            "signal": 10,
            "tooltip": true
        },
        "custom/wlogout": { 
            "format": "", 
            "tooltip": "true",
            "on-click": "wlogout --buttons-per-row 5", 
            "on-click-right": "wlogout --buttons-per-row 5 -p layer-shell"
        },
        "custom/pacman": {
            "format": "󰅢 {}",
            "interval": 30,   
            "exec": "checkupdates | wc -l",
            "exec-if": "exit 0",
            "on-click": "alacritty --class pacman-update --command sh -c 'yay -Syu; echo Done - Press enter to exit; read'; pkill -SIGRTMIN+8 waybar",
            "signal": 8,
            "tooltip": false,
        },
        "custom/expand": {
            "format": "",
            "tooltip": false
        },
        "custom/endpoint":{
            "format": "|",
            "tooltip": false
        },
        "group/expand": {
            "orientation": "horizontal",
            "drawer": {
                "transition-duration": 600,
                "transition-to-left": true,
                "click-to-reveal": true
            },
            "modules": ["custom/expand","custom/hyprshade","cpu","memory","temperature","custom/endpoint"],
        },
        "custom/brightness": {
            "format": "󰃟",
            "on-click": "~/.config/waybar_popups/manager.py brightness",
            "tooltip": false
        },
        "backlight": {
            "format": "󰃞 {percent}%",
            "on-click": "uwsm-app -- python3 ~/.config/hypr/scripts/brightness-control.py",
            "tooltip-format": "Laptop brightness: {percent}%"
        },
        "custom/hyprshade": {
            "format": "{}",
            "return-type": "json",
            "interval": "once",
            "exec": "~/.config/waybar/scripts/hyprshade-status.sh",
            "on-click": "~/.config/waybar/scripts/hyprshade-toggle.sh && pkill -SIGRTMIN+12 waybar",
            "on-click-right": "~/.config/waybar/scripts/hyprshade-select.sh && pkill -SIGRTMIN+12 waybar",
            "signal": 12,
            "tooltip": true
        },
        "cpu": {
            "format": "󰻠",
            "tooltip": true
        },
        "memory": {
            "format": ""
        },
        "temperature": {
            "critical-threshold": 80,
            "format": "",
        },
        "tray": {
            "icon-size": 14,
            "spacing": 10,
            "ignored-items": ["blueman-applet", "bluetooth"]
        },
} 


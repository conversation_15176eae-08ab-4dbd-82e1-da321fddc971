#!/bin/bash

# Optimized Virtual Desktop Waybar Module
# Uses printdesk command for faster single vdesk queries

VDESK_NUM=$1
ACTION=$2

# Exit if invalid input
[[ -z $VDESK_NUM || ! $VDESK_NUM =~ ^[1-5]$ ]] && echo '{"text":"","class":"hidden"}' && exit

# Handle click action - no waybar signal needed (monitor handles it)
if [[ $ACTION == "click" ]]; then
    hyprctl dispatch vdesk $VDESK_NUM
    exit
fi

# Use printdesk for single vdesk query (much faster than printstate)
VDESK_INFO=$(hyprctl printdesk $VDESK_NUM 2>/dev/null)

# Exit if vdesk doesn't exist
[[ -z $VDESK_INFO ]] && echo '{"text":"","class":"hidden"}' && exit

# Extract name from "Virtual desk 1:    Focus" (simple and robust)
VDESK_NAME=$(echo "$VDESK_INFO" | cut -d':' -f2- | xargs)

# Get detailed status from printstate (only when needed for display)
STATE=$(hyprctl printstate)
VDESK_DETAIL=$(echo "$STATE" | grep ": $VDESK_NUM$" -A3 | head -4)

# Quick exit if not found in detailed state
[[ -z $VDESK_DETAIL ]] && echo '{"text":"","class":"hidden"}' && exit

# Check status with simple greps
FOCUSED=$(echo "$VDESK_DETAIL" | grep -q "Focused: true" && echo "true" || echo "false")
POPULATED=$(echo "$VDESK_DETAIL" | grep -q "Populated: true" && echo "true" || echo "false")

# Hide if empty and not focused
[[ $POPULATED == "false" && $FOCUSED == "false" ]] && echo '{"text":"","class":"hidden"}' && exit

# Output result
CLASS=$([[ $FOCUSED == "true" ]] && echo "vdesk-focused" || echo "vdesk-unfocused")
echo "{\"text\":\"$VDESK_NAME\",\"class\":\"$CLASS\"}"
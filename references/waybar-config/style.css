@import "colors.css";

* {
    font-size:15px;
    font-family: "CodeNewRoman Nerd Font Propo";
}
window#waybar{
    all:unset;
}
.modules-left {
    padding:7px;
    margin:10 0 5 10;
    border-radius:10px;
    background: alpha(@background,.6);
    box-shadow: 0px 0px 2px rgba(0, 0, 0, .6);
}
.modules-center {
    padding:7px;
    margin:10 0 5 0;
    border-radius:10px;
    background: alpha(@background,.6);
    box-shadow: 0px 0px 2px rgba(0, 0, 0, .6);
}
.modules-right {
    padding:7px;
    margin: 10 10 5 0;
    border-radius:10px;
    background: alpha(@background,.6);
    box-shadow: 0px 0px 2px rgba(0, 0, 0, .6);
}
tooltip {
    background:@background;
    color: @on_surface;
}
#clock:hover, #custom-pacman:hover, #custom-notification:hover,#bluetooth:hover,#network:hover,#pulseaudio:hover,#battery:hover,#custom-power-profile:hover, #cpu:hover,#memory:hover,#temperature:hover{
    transition: all .3s ease;
    color: @primary;
}
#custom-notification {
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;
}
#clock{
    padding: 0px 5px;
    color: @on_surface;
    transition: all .3s ease;
}
#custom-pacman{
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;

}
#workspaces {
    padding: 0px 5px;
}
#workspaces button {
    all:unset;
    padding: 0px 5px;
    color: alpha(@primary,.4);
    transition: all .2s ease;
}
#workspaces button:hover {
    color:rgba(0,0,0,0);
    border: none;
    text-shadow: 0px 0px 1.5px rgba(0, 0, 0, .5);
    transition: all 1s ease;
}
#workspaces button.active {
    color: @primary;
    border: none;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, .5);
}
#workspaces button.empty {
    color: rgba(0,0,0,0);
    border: none;
    text-shadow: 0px 0px 1.5px rgba(0, 0, 0, .2);
}
#workspaces button.empty:hover {
    color: rgba(0,0,0,0);
    border: none;
    text-shadow: 0px 0px 1.5px rgba(0, 0, 0, .5);
    transition: all 1s ease;
}
#workspaces button.empty.active {
    color: @primary;
    border: none;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, .5);
}
#bluetooth{
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;

}
#network{
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;

}
#pulseaudio{
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;

}
#battery{
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;


}
#battery.charging {
    color: #26A65B;
}

#battery.warning:not(.charging) {
    color: #ffbe61;
}

#battery.critical:not(.charging) {
    color: #f53c3c;
    animation-name: blink;
    animation-duration: 0.5s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-direction: alternate;
}
#custom-power-profile{
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;
}
#custom-wlogout{
    padding: 0px 5px;
    transition: all .3s ease;
    color: @on_surface;
}

#custom-brightness {
    padding: 0 8px;
    margin: 0 2px;
    border-radius: 8px;
    background: @surface_container;
    color: @primary;
}

#custom-brightness:hover {
    background: @primary;
    color: @on_primary;
}
#group-expand{
    padding: 0px 5px;
    transition: all .3s ease; 
}
#custom-expand{
    padding: 0px 5px;
    color: alpha(@on_surface,.2);
    text-shadow: 0px 0px 2px rgba(0, 0, 0, .7);
    transition: all .3s ease; 
}
#custom-expand:hover{
    color:rgba(255,255,255,.2);
    text-shadow: 0px 0px 2px rgba(255, 255, 255, .5);
}
#custom-colorpicker{
    padding: 0px 5px;
}
#cpu,#memory,#temperature{
    padding: 0px 5px;
    transition: all .3s ease; 
    color: @on_surface;

}
#custom-endpoint{
    color:transparent;
    text-shadow: 0px 0px 1.5px rgba(0, 0, 0, 1);

}
#tray{
    padding: 0px 5px;
    transition: all .3s ease; 

}
#tray menu * {
    padding: 0px 5px;
    transition: all .3s ease; 
}

#tray menu separator {
    padding: 0px 5px;
    transition: all .3s ease; 
}

/* Individual Virtual Desktop Buttons - Material You Styling */
#custom-vdesk-1, #custom-vdesk-2, #custom-vdesk-3, #custom-vdesk-4, #custom-vdesk-5 {
    padding: 0px 8px;
    margin: 0 2px;
    transition: all .15s ease;
    border-radius: 4px;
}

/* Hidden virtual desktops (empty) - smooth disappearance */
#custom-vdesk-1.hidden, #custom-vdesk-2.hidden, #custom-vdesk-3.hidden, #custom-vdesk-4.hidden, #custom-vdesk-5.hidden {
    padding: 0;
    margin: 0;
    opacity: 0;
    transition: all .15s ease;
}

/* Focused virtual desktop */
#custom-vdesk-1.vdesk-focused, #custom-vdesk-2.vdesk-focused, #custom-vdesk-3.vdesk-focused, #custom-vdesk-4.vdesk-focused, #custom-vdesk-5.vdesk-focused {
    color: @primary;
    font-weight: bold;
    text-shadow: 0px 0px 2px alpha(@primary, 0.3);
    background: alpha(@primary, 0.1);
}

/* Unfocused virtual desktop */
#custom-vdesk-1.vdesk-unfocused, #custom-vdesk-2.vdesk-unfocused, #custom-vdesk-3.vdesk-unfocused, #custom-vdesk-4.vdesk-unfocused, #custom-vdesk-5.vdesk-unfocused {
    color: alpha(@on_surface, 0.7);
    transition: all .15s ease;
}

/* Hover effects */
#custom-vdesk-1.vdesk-unfocused:hover, #custom-vdesk-2.vdesk-unfocused:hover, #custom-vdesk-3.vdesk-unfocused:hover, #custom-vdesk-4.vdesk-unfocused:hover, #custom-vdesk-5.vdesk-unfocused:hover {
    color: @primary;
    background: alpha(@primary, 0.05);
    transition: all .15s ease;
}

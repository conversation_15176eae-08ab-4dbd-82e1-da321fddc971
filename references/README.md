# Virtual Desktops CFFI Module Reference

This folder contains all the relevant files from the current virtual desktop implementation that will be needed as reference for implementing the CFFI module described in `VIRTUAL_DESKTOPS_CPP_MODULE_PRD.md`.

## Directory Structure

```
virtual-desktops-cffi-reference/
├── hyprland-virtual-desktops/          # Complete C++ plugin source code
│   ├── src/                            # Plugin implementation
│   ├── include/                        # Header files
│   ├── virtual-desktops.so            # Compiled plugin binary
│   └── build files (CMakeLists.txt, etc.)
├── waybar-config/                      # Current waybar integration
│   ├── config                         # Waybar config with 5 vdesk modules
│   ├── style.css                      # CSS styling for virtual desktop buttons
│   └── virtual-desktop.sh             # Core shell script for vdesk display
├── hyprland-config/                    # Hyprland configuration
│   ├── hyprland.conf                  # Main config with plugin setup
│   ├── binds.conf                     # Virtual desktop keybindings
│   └── waybar-vdesk-monitor.sh        # IPC event monitor script
├── documentation/                      # Implementation documentation
│   ├── VIRTUAL_DESKTOP_SETUP.md       # Setup guide
│   ├── VIRTUAL_DESKTOPS_CPP_MODULE_PRD.md  # CFFI module requirements
│   └── virtual-desktops-implementation-notes.md  # Implementation notes
└── waybar-popup-manager.service        # Related systemd service
```

## Key Components to Reference

### 1. Current Waybar Integration Logic
- **`waybar-config/virtual-desktop.sh`** - Core logic for formatting and displaying virtual desktops
- **`waybar-config/config`** - Current module configuration pattern (5 separate modules)
- **`waybar-config/style.css`** - CSS classes and styling approach

### 2. Hyprland Plugin Interface
- **`hyprland-virtual-desktops/src/`** - C++ plugin implementation showing IPC commands and events
- **`hyprland-config/waybar-vdesk-monitor.sh`** - Current IPC event monitoring approach

### 3. Virtual Desktop States & Logic
- **Desktop Names**: Focus, Research, Comms, Media, Misc
- **States**: focused, populated, empty, hidden
- **Display Logic**: Show/hide based on population, visual indicators

### 4. Current Performance Characteristics
- **Shell Script Overhead**: Multiple script executions per update
- **IPC Monitoring**: Separate systemd service for event listening
- **Update Mechanism**: SIGRTMIN+8 signals to waybar modules

## Implementation Migration Path

The CFFI module should:
1. **Replace** the 5 separate waybar modules with a single unified module
2. **Eliminate** the need for `waybar-vdesk-monitor.sh` systemd service
3. **Integrate** the logic from `virtual-desktop.sh` into native code
4. **Maintain** the same visual appearance and functionality
5. **Improve** performance by eliminating shell script overhead

## Usage Notes

- The current implementation works well but has performance overhead
- Virtual desktop names and icons are configurable
- The CSS styling provides good visual feedback for different states
- IPC events from the Hyprland plugin drive real-time updates
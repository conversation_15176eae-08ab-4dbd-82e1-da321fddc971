# -----------------------------------------------------
# Key bindings
# name: "Default"
# -----------------------------------------------------

# SUPER KEY
$mainMod = SUPER
$HYPRSCRIPTS = ~/.config/hypr/scripts
# $SCRIPTS = ~/.config/ml4w/scripts

# Applications
bind = $mainMod, RETURN, exec, $terminal    # Open the terminal
bind = $mainMod, B, exec, $browser       # Open the browser
bind = $mainMod, E, exec, $fileManager     # Open the filemanager
bind = $mainMod, W, exec, uwsm-app -- waypaper # Open wallpaper manager
bind = $mainMod SHIFT, W, exec, uwsm-app -- waypaper --random # Set random wallpaper
bind = $mainMod CTRL, RETURN, exec, pkill fuzzel || fuzzel
# bind = $mainMod CTRL, E, exec, ~/.config/ml4w/settings/emojipicker.sh # Open the emoji picker
# bind = $mainMod CTRL, C, exec, ~/.config/ml4w/settings/calculator.sh  # Open the calculator

# Windows
bind = $mainMod, Q, killactive                                                              # Kill active window
bind = $mainMod SHIFT, Q, exec, hyprctl activewindow | grep pid | tr -d 'pid:' | xargs kill # Quit active window and all open instances
bind = $mainMod, F, fullscreen, 0                                                           # Set active window to fullscreen
bind = $mainMod, M, fullscreen, 1                                                           # Maximize Window
bind = $mainMod, T, togglefloating                                                          # Toggle active windows into floating mode
bind = $mainMod SHIFT, T, workspaceopt, allfloat                                            # Toggle all windows into floating mode
bind = $mainMod, J, togglesplit                                                             # Toggle split
bind = $mainMod, left, movefocus, l                                                         # Move focus left
bind = $mainMod, right, movefocus, r                                                        # Move focus right
bind = $mainMod, up, movefocus, u                                                           # Move focus up
bind = $mainMod, down, movefocus, d                                                         # Move focus down
bindm = $mainMod, mouse:272, movewindow                                                     # Move window with the mouse
bindm = $mainMod, mouse:273, resizewindow                                                   # Resize window with the mouse
bind = $mainMod SHIFT, right, resizeactive, 100 0                                           # Increase window width with keyboard
bind = $mainMod SHIFT, left, resizeactive, -100 0                                           # Reduce window width with keyboard
bind = $mainMod SHIFT, down, resizeactive, 0 100                                            # Increase window height with keyboard
bind = $mainMod SHIFT, up, resizeactive, 0 -100                                             # Reduce window height with keyboard
bind = $mainMod, G, togglegroup                                                             # Toggle window group
bind = $mainMod, K, swapsplit                                                               # Swapsplit
bind = $mainMod ALT, left, swapwindow, l                                                    # Swap tiled window left
bind = $mainMod ALT, right, swapwindow, r                                                   # Swap tiled window right
bind = $mainMod ALT, up, swapwindow, u                                                      # Swap tiled window up
bind = $mainMod ALT, down, swapwindow, d                                                    # Swap tiled window down
binde = ALT,Tab,cyclenext                                                                   # Cycle between windows
binde = ALT,Tab,bringactivetotop                                                            # Bring active window to the top

# Actions
bind = $mainMod CTRL, R, exec, hyprctl reload                                             # Reload Hyprland configuration
bind = $mainMod SHIFT, A, exec, $HYPRSCRIPTS/toggle-animations.sh                         # Toggle animations
# Screenshots with hyprshot
bind = $mainMod, S, exec, $HYPRSCRIPTS/hyprshot-menu.sh                                   # Open screenshot menu
bind = $mainMod SHIFT, S, exec, hyprshot -m region -o ~/Pictures/screenshots --freeze   # Screenshot region to clipboard and save
bind = $mainMod CTRL, S, exec, hyprshot -m output -o ~/Pictures/screenshots --freeze     # Screenshot monitor to clipboard and save
bind = , PRINT, exec, hyprshot -m output -o ~/Pictures/screenshots                       # Screenshot monitor to clipboard and save
bind = $mainMod CTRL, Q, exec, ~/.config/ml4w/scripts/wlogout.sh                          # Start wlogout
bind = $mainMod CTRL, K, exec, $HYPRSCRIPTS/keybindings.sh                                # Show keybindings

# Multi-Monitor Management
bind = $mainMod CTRL, left, focusmonitor, l    # Focus monitor to the left
bind = $mainMod CTRL, right, focusmonitor, r   # Focus monitor to the right
bind = $mainMod CTRL SHIFT, left, movewindow, mon:l   # Move window to left monitor
bind = $mainMod CTRL SHIFT, right, movewindow, mon:r  # Move window to right monitor
bind = $mainMod CTRL SHIFT, down, movetoworkspace, empty  # Move window to empty workspace
bind = $mainMod SHIFT, B, exec, systemctl --user restart waybar                               # Reload waybar
bind = $mainMod CTRL, B, exec, ~/.config/waybar/toggle.sh                                 # Toggle waybar
bind = $mainMod SHIFT, R, exec, $HYPRSCRIPTS/loadconfig.sh                                # Reload hyprland config
bind = $mainMod, V, exec, $SCRIPTS/cliphist.sh                                            # Open clipboard manager
bind = $mainMod CTRL, T, exec, ~/.config/waybar/themeswitcher.sh                          # Open waybar theme switcher
bind = $mainMod CTRL, S, exec, flatpak run com.ml4w.settings                              # Open ML4W Dotfiles Settings app
bind = $mainMod SHIFT, H, exec, $HYPRSCRIPTS/hyprshade.sh                                 # Toggle screenshader
bind = $mainMod ALT, G, exec, $HYPRSCRIPTS/gamemode.sh                                    # Toggle game mode
bind = $mainMod CTRL, L, exec, ~/.config/hypr/scripts/power.sh lock                       # Start wlogout
bind = $mainMod, L, exec, hyprlock                                                          # Lock screen (MX Keys lock key)

# Virtual Desktops (Primary bindings)
bind = $mainMod, 1, vdesk, 1      # Switch to virtual desktop 1 (Code)
bind = $mainMod, 2, vdesk, 2      # Switch to virtual desktop 2 (Web)
bind = $mainMod, 3, vdesk, 3      # Switch to virtual desktop 3 (Design)
bind = $mainMod, 4, vdesk, 4      # Switch to virtual desktop 4 (Chat)
bind = $mainMod, 5, vdesk, 5      # Switch to virtual desktop 5 (Media)

bind = $mainMod SHIFT, 1, movetodesk, 1   # Move active window to virtual desktop 1
bind = $mainMod SHIFT, 2, movetodesk, 2   # Move active window to virtual desktop 2
bind = $mainMod SHIFT, 3, movetodesk, 3   # Move active window to virtual desktop 3
bind = $mainMod SHIFT, 4, movetodesk, 4   # Move active window to virtual desktop 4
bind = $mainMod SHIFT, 5, movetodesk, 5   # Move active window to virtual desktop 5

bind = $mainMod, Tab, cyclevdesks       # Cycle through populated virtual desktops
bind = $mainMod SHIFT, Tab, backcyclevdesks # Cycle through populated virtual desktops (reverse)

bind = $mainMod CTRL, 1, exec, $HYPRSCRIPTS/moveTo.sh 1  # Move all windows to workspace 1
bind = $mainMod CTRL, 2, exec, $HYPRSCRIPTS/moveTo.sh 2  # Move all windows to workspace 2
bind = $mainMod CTRL, 3, exec, $HYPRSCRIPTS/moveTo.sh 3  # Move all windows to workspace 3
bind = $mainMod CTRL, 4, exec, $HYPRSCRIPTS/moveTo.sh 4  # Move all windows to workspace 4
bind = $mainMod CTRL, 5, exec, $HYPRSCRIPTS/moveTo.sh 5  # Move all windows to workspace 5
bind = $mainMod CTRL, 6, exec, $HYPRSCRIPTS/moveTo.sh 6  # Move all windows to workspace 6
bind = $mainMod CTRL, 7, exec, $HYPRSCRIPTS/moveTo.sh 7  # Move all windows to workspace 7
bind = $mainMod CTRL, 8, exec, $HYPRSCRIPTS/moveTo.sh 8  # Move all windows to workspace 8
bind = $mainMod CTRL, 9, exec, $HYPRSCRIPTS/moveTo.sh 9  # Move all windows to workspace 9
bind = $mainMod CTRL, 0, exec, $HYPRSCRIPTS/moveTo.sh 10 # Move all windows to workspace 10

bind = $mainMod, mouse_down, workspace, e+1  # Open next workspace
bind = $mainMod, mouse_up, workspace, e-1    # Open previous workspace
bind = $mainMod CTRL, down, workspace, empty # Open the next empty workspace


# Fn keys
bindle = , XF86MonBrightnessUp, exec, brightnessctl -q s +10%                                                                                                  # Increase brightness by 10%
bindle = , XF86MonBrightnessDown, exec, brightnessctl -q s 10%-                                                                                                # Reduce brightness by 10%
bindle=, XF86AudioRaiseVolume, exec, wpctl set-volume -l 1 @DEFAULT_AUDIO_SINK@ 2%+                                                                          # Increase volume by 5% (max 100% limit also added hold to raise volume)
bindle=, XF86AudioLowerVolume, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 2%-                                                                               # Reduce volume by 5% (min 0% limit also added hold to lower volume)
bind = , XF86AudioMute, exec, pactl set-sink-mute @DEFAULT_SINK@ toggle                                                                                      # Toggle mute
bind = , XF86AudioPlay, exec, playerctl play-pause                                                                                                           # Audio play pause
bind = , XF86AudioPause, exec, playerctl pause                                                                                                               # Audio pause
bind = , XF86AudioNext, exec, playerctl next                                                                                                                 # Audio next
bind = , XF86AudioPrev, exec, playerctl previous                                                                                                             # Audio previous
bind = , XF86AudioMicMute, exec, pactl set-source-mute @DEFAULT_SOURCE@ toggle                                                                               # Toggle microphone
bind = , XF86Lock, exec, hyprlock                                                                                                                            # Open screenlock
bind = , XF86Tools, exec, flatpak run com.ml4w.settings
bind = , XF86PowerOff, exec, systemctl suspend                                                                                                                   # Sleep on power button                                                                                            

bind = , code:238, exec, brightnessctl -d smc::kbd_backlight s +10
bind = , code:237, exec, brightnessctl -d smc::kbd_backlight s 10-


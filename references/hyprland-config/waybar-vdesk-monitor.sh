#!/bin/bash

# Simplified Waybar Virtual Desktop Monitor
# Listens specifically to vdesk IPC events and triggers waybar updates

set -euo pipefail

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] vdesk-monitor: $1" >&2
}

# Signal handlers for clean shutdown
cleanup() {
    log "Shutting down..."
    exit 0
}
trap cleanup SIGTERM SIGINT

# Wait for <PERSON>yp<PERSON> and get socket path
wait_for_hyprland() {
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if pgrep -x "Hyprland" > /dev/null && [[ -n "${HYPRLAND_INSTANCE_SIGNATURE:-}" ]]; then
            local socket_path="${XDG_RUNTIME_DIR}/hypr/${HYPRLAND_INSTANCE_SIGNATURE}/.socket2.sock"
            if [[ -S "$socket_path" ]]; then
                echo "$socket_path"
                return 0
            fi
        fi
        sleep 1
        ((attempt++))
    done
    
    log "ERROR: Hyprland not ready after $max_attempts seconds"
    exit 1
}

# Main monitoring function
monitor_vdesk_events() {
    local socket_path=$(wait_for_hyprland)
    log "Monitoring vdesk events on: $socket_path"
    
    while pgrep -x "Hyprland" > /dev/null; do
        socat -u UNIX-CONNECT:"$socket_path" - 2>/dev/null | while IFS= read -r line; do
            # Only trigger on vdesk changes
            if [[ $line =~ ^vdesk\>\>(.+)$ ]]; then
                log "vdesk changed to: ${BASH_REMATCH[1]}"
                pgrep -x waybar | xargs -I {} kill -s SIGRTMIN+8 {} 2>/dev/null || true
            fi
        done
        
        # Reconnect after socket closes
        log "Socket closed, reconnecting in 2 seconds..."
        sleep 2
    done
    
    log "Hyprland stopped, exiting"
}

log "Starting simplified vdesk monitor..."
monitor_vdesk_events
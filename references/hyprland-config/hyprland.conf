
#  _   _                  _                 _  
# | | | |_   _ _ __  _ __| | __ _ _ __   __| | 
# | |_| | | | | '_ \| '__| |/ _` | '_ \ / _` | 
# |  _  | |_| | |_) | |  | | (_| | | | | (_| | 
# |_| |_|\__, | .__/|_|  |_|\__,_|_| |_|\__,_| 
#        |___/|_|                              
#  
# ----------------------------------------------------- 
# Full documentation https://wiki.hyprland.org

source = ~/.config/hypr/colors.conf

$terminal = uwsm-app -- alacritty
$fileManager = uwsm-app -- thunar
$menu = fuzzel
$browser = uwsm-app -- zen-browser

# Virtual Desktops Plugin Configuration (TESTING)
# plugin {
#     virtual-desktops {
#         names = 1:  Focus, 2:󰍉 Research, 3:󰵅  <PERSON><PERSON><PERSON>, 4:󰋋 Media, 5:󰆧 Misc
#         # 1: Focus (Thesis, QF/S&C programming)
#         # 2: Research (Browser, PDFs, Notes) 
#         # 3: Comms (<PERSON><PERSON>, <PERSON>la<PERSON>, <PERSON>rd, Calendar)
#         # 4: Media (Spotify, YouTube, Games)
#         # 5: Misc (Scratchpad, System Monitor, Files)
#         cycleworkspaces = 0
#         rememberlayout = none
#         notifyinit = 1
#         cycle_populated_only=1
#         verbose_logging = 0
#     }
# }
#debug config
# Virtual Desktops Plugin Configuration (TESTING)
plugin {
    virtual-desktops {
        names = 1:  Focus, 2:󰍉 Research, 3:󰵅  Comms, 4:󰋋 Media, 5:󰆧 Misc
        # 1: Focus (Thesis, QF/S&C programming)
        # 2: Research (Browser, PDFs, Notes) 
        # 3: Comms (Email, Slack, Discord, Calendar)
        # 4: Media (Spotify, YouTube, Games)
        # 5: Misc (Scratchpad, System Monitor, Files)
        cycleworkspaces = 0
        rememberlayout = none
        notifyinit = 1
        cycle_populated_only=1
        verbose_logging = 1
    }
}
bind = SUPER, period , cyclevdesks       # Cycle through populated virtual desktops
bind = SUPER ,comma, backcyclevdesks # Cycle through populated virtual desktops (reverse)

# Auto-reload plugins on startup
exec-once = hyprpm reload -n

# Restore wallpaper after a delay to ensure hyprpaper is ready
exec-once = sleep 3 && waypaper --restore

# Apply sun-based screen shader on startup
exec = ~/.config/hypr/scripts/hyprshade-sun-schedule.sh apply

source = ~/.config/hypr/conf/monitor.conf
source = ~/.config/hypr/conf/input.conf
source = ~/.config/hypr/conf/general.conf
source = ~/.config/hypr/conf/decoration.conf
source = ~/.config/hypr/conf/animations.conf
source = ~/.config/hypr/conf/layouts.conf
source = ~/.config/hypr/conf/gestures.conf
source = ~/.config/hypr/conf/misc.conf
source = ~/.config/hypr/conf/windowrules.conf
source = ~/.config/hypr/conf/binds.conf

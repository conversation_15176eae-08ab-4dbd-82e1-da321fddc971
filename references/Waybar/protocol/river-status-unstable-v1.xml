<?xml version="1.0" encoding="UTF-8"?>
<protocol name="river_status_unstable_v1">
  <copyright>
    Copyright 2020 The River Developers

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted, provided that the above
    copyright notice and this permission notice appear in all copies.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
    WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
    MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
    ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
    WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
    ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
    OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
  </copyright>

  <interface name="zriver_status_manager_v1" version="4">
    <description summary="manage river status objects">
      A global factory for objects that receive status information specific
      to river. It could be used to implement, for example, a status bar.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the river_status_manager object">
        This request indicates that the client will not use the
        river_status_manager object any more. Objects that have been created
        through this instance are not affected.
      </description>
    </request>

    <request name="get_river_output_status">
      <description summary="create an output status object">
        This creates a new river_output_status object for the given wl_output.
      </description>
      <arg name="id" type="new_id" interface="zriver_output_status_v1"/>
      <arg name="output" type="object" interface="wl_output"/>
    </request>

    <request name="get_river_seat_status">
      <description summary="create a seat status object">
        This creates a new river_seat_status object for the given wl_seat.
      </description>
      <arg name="id" type="new_id" interface="zriver_seat_status_v1"/>
      <arg name="seat" type="object" interface="wl_seat"/>
    </request>
  </interface>

  <interface name="zriver_output_status_v1" version="4">
    <description summary="track output tags and focus">
      This interface allows clients to receive information about the current
      windowing state of an output.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the river_output_status object">
        This request indicates that the client will not use the
        river_output_status object any more.
      </description>
    </request>

    <event name="focused_tags">
      <description summary="focused tags of the output">
        Sent once binding the interface and again whenever the tag focus of
        the output changes.
      </description>
      <arg name="tags" type="uint" summary="32-bit bitfield"/>
    </event>

    <event name="view_tags">
      <description summary="tag state of an output's views">
        Sent once on binding the interface and again whenever the tag state
        of the output changes.
      </description>
      <arg name="tags" type="array" summary="array of 32-bit bitfields"/>
    </event>

    <event name="urgent_tags" since="2">
      <description summary="tags of the output with an urgent view">
        Sent once on binding the interface and again whenever the set of
        tags with at least one urgent view changes.
      </description>
      <arg name="tags" type="uint" summary="32-bit bitfield"/>
    </event>

    <event name="layout_name" since="4">
      <description summary="name of the layout">
        Sent once on binding the interface should a layout name exist and again
        whenever the name changes.
      </description>
      <arg name="name" type="string" summary="layout name"/>
    </event>

    <event name="layout_name_clear" since="4">
      <description summary="name of the layout">
        Sent when the current layout name has been removed without a new one
        being set, for example when the active layout generator disconnects.
      </description>
    </event>
  </interface>

  <interface name="zriver_seat_status_v1" version="3">
    <description summary="track seat focus">
      This interface allows clients to receive information about the current
      focus of a seat. Note that (un)focused_output events will only be sent
      if the client has bound the relevant wl_output globals.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the river_seat_status object">
        This request indicates that the client will not use the
        river_seat_status object any more.
      </description>
    </request>

    <event name="focused_output">
      <description summary="the seat focused an output">
        Sent on binding the interface and again whenever an output gains focus.
      </description>
      <arg name="output" type="object" interface="wl_output"/>
    </event>

    <event name="unfocused_output">
      <description summary="the seat unfocused an output">
        Sent whenever an output loses focus.
      </description>
      <arg name="output" type="object" interface="wl_output"/>
    </event>

    <event name="focused_view">
      <description summary="information on the focused view">
        Sent once on binding the interface and again whenever the focused
        view or a property thereof changes. The title may be an empty string
        if no view is focused or the focused view did not set a title.
      </description>
      <arg name="title" type="string" summary="title of the focused view"/>
    </event>

    <event name="mode" since="3">
      <description summary="the active mode changed">
        Sent once on binding the interface and again whenever a new mode
        is entered (e.g. with riverctl enter-mode foobar).
      </description>
      <arg name="name" type="string" summary="name of the mode"/>
    </event>
  </interface>
</protocol>

<?xml version="1.0" encoding="UTF-8"?>
<protocol name="wlr_foreign_toplevel_management_unstable_v1">
  <copyright>
    Copyright © 2018 <PERSON><PERSON>

    Permission to use, copy, modify, distribute, and sell this
    software and its documentation for any purpose is hereby granted
    without fee, provided that the above copyright notice appear in
    all copies and that both that copyright notice and this permission
    notice appear in supporting documentation, and that the name of
    the copyright holders not be used in advertising or publicity
    pertaining to distribution of the software without specific,
    written prior permission.  The copyright holders make no
    representations about the suitability of this software for any
    purpose.  It is provided "as is" without express or implied
    warranty.

    THE COPYRIGHT HOLDERS DISCLAIM ALL WARRANTIES WITH REGARD TO THIS
    SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
    FITNESS, IN NO EVENT SHALL THE COPYRIGHT HOLDERS BE LIABLE FOR ANY
    SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
    WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN
    AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR OTHER TORTIOUS ACTION,
    ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF
    THIS SOFTWARE.
  </copyright>

  <interface name="zwlr_foreign_toplevel_manager_v1" version="3">
    <description summary="list and control opened apps">
      The purpose of this protocol is to enable the creation of taskbars
      and docks by providing them with a list of opened applications and
      letting them request certain actions on them, like maximizing, etc.

      After a client binds the zwlr_foreign_toplevel_manager_v1, each opened
      toplevel window will be sent via the toplevel event
    </description>

    <event name="toplevel">
      <description summary="a toplevel has been created">
        This event is emitted whenever a new toplevel window is created. It
        is emitted for all toplevels, regardless of the app that has created
        them.

        All initial details of the toplevel(title, app_id, states, etc.) will
        be sent immediately after this event via the corresponding events in
        zwlr_foreign_toplevel_handle_v1.
      </description>
      <arg name="toplevel" type="new_id" interface="zwlr_foreign_toplevel_handle_v1"/>
    </event>

    <request name="stop">
      <description summary="stop sending events">
        Indicates the client no longer wishes to receive events for new toplevels.
        However the compositor may emit further toplevel_created events, until
        the finished event is emitted.

        The client must not send any more requests after this one.
      </description>
    </request>

    <event name="finished">
      <description summary="the compositor has finished with the toplevel manager">
        This event indicates that the compositor is done sending events to the
        zwlr_foreign_toplevel_manager_v1. The server will destroy the object
        immediately after sending this request, so it will become invalid and
        the client should free any resources associated with it.
      </description>
    </event>
  </interface>

  <interface name="zwlr_foreign_toplevel_handle_v1" version="3">
    <description summary="an opened toplevel">
      A zwlr_foreign_toplevel_handle_v1 object represents an opened toplevel
      window. Each app may have multiple opened toplevels.

      Each toplevel has a list of outputs it is visible on, conveyed to the
      client with the output_enter and output_leave events.
    </description>

    <event name="title">
      <description summary="title change">
        This event is emitted whenever the title of the toplevel changes.
      </description>
      <arg name="title" type="string"/>
    </event>

    <event name="app_id">
      <description summary="app-id change">
        This event is emitted whenever the app-id of the toplevel changes.
      </description>
      <arg name="app_id" type="string"/>
    </event>

    <event name="output_enter">
      <description summary="toplevel entered an output">
        This event is emitted whenever the toplevel becomes visible on
        the given output. A toplevel may be visible on multiple outputs.
      </description>
      <arg name="output" type="object" interface="wl_output"/>
    </event>

    <event name="output_leave">
      <description summary="toplevel left an output">
        This event is emitted whenever the toplevel stops being visible on
        the given output. It is guaranteed that an entered-output event
        with the same output has been emitted before this event.
      </description>
      <arg name="output" type="object" interface="wl_output"/>
    </event>

    <request name="set_maximized">
      <description summary="requests that the toplevel be maximized">
        Requests that the toplevel be maximized. If the maximized state actually
        changes, this will be indicated by the state event.
      </description>
    </request>

    <request name="unset_maximized">
      <description summary="requests that the toplevel be unmaximized">
        Requests that the toplevel be unmaximized. If the maximized state actually
        changes, this will be indicated by the state event.
      </description>
    </request>

    <request name="set_minimized">
      <description summary="requests that the toplevel be minimized">
        Requests that the toplevel be minimized. If the minimized state actually
        changes, this will be indicated by the state event.
      </description>
    </request>

    <request name="unset_minimized">
      <description summary="requests that the toplevel be unminimized">
        Requests that the toplevel be unminimized. If the minimized state actually
        changes, this will be indicated by the state event.
      </description>
    </request>

    <request name="activate">
      <description summary="activate the toplevel">
        Request that this toplevel be activated on the given seat.
        There is no guarantee the toplevel will be actually activated.
      </description>
      <arg name="seat" type="object" interface="wl_seat"/>
    </request>

    <enum name="state">
      <description summary="types of states on the toplevel">
        The different states that a toplevel can have. These have the same meaning
        as the states with the same names defined in xdg-toplevel
      </description>

      <entry name="maximized"  value="0" summary="the toplevel is maximized"/>
      <entry name="minimized"  value="1" summary="the toplevel is minimized"/>
      <entry name="activated"  value="2" summary="the toplevel is active"/>
      <entry name="fullscreen" value="3" summary="the toplevel is fullscreen" since="2"/>
    </enum>

    <event name="state">
      <description summary="the toplevel state changed">
        This event is emitted immediately after the zlw_foreign_toplevel_handle_v1
        is created and each time the toplevel state changes, either because of a
        compositor action or because of a request in this protocol.
      </description>

      <arg name="state" type="array"/>
    </event>

    <event name="done">
      <description summary="all information about the toplevel has been sent">
        This event is sent after all changes in the toplevel state have been
        sent.

        This allows changes to the zwlr_foreign_toplevel_handle_v1 properties
        to be seen as atomic, even if they happen via multiple events.
      </description>
    </event>

    <request name="close">
      <description summary="request that the toplevel be closed">
        Send a request to the toplevel to close itself. The compositor would
        typically use a shell-specific method to carry out this request, for
        example by sending the xdg_toplevel.close event. However, this gives
        no guarantees the toplevel will actually be destroyed. If and when
        this happens, the zwlr_foreign_toplevel_handle_v1.closed event will
        be emitted.
      </description>
    </request>

    <request name="set_rectangle">
      <description summary="the rectangle which represents the toplevel">
        The rectangle of the surface specified in this request corresponds to
        the place where the app using this protocol represents the given toplevel.
        It can be used by the compositor as a hint for some operations, e.g
        minimizing. The client is however not required to set this, in which
        case the compositor is free to decide some default value.

        If the client specifies more than one rectangle, only the last one is
        considered.

        The dimensions are given in surface-local coordinates.
        Setting width=height=0 removes the already-set rectangle.
      </description>

      <arg name="surface" type="object" interface="wl_surface"/>
      <arg name="x" type="int"/>
      <arg name="y" type="int"/>
      <arg name="width" type="int"/>
      <arg name="height" type="int"/>
    </request>

    <enum name="error">
      <entry name="invalid_rectangle" value="0"
        summary="the provided rectangle is invalid"/>
    </enum>

    <event name="closed">
      <description summary="this toplevel has been destroyed">
        This event means the toplevel has been destroyed. It is guaranteed there
        won't be any more events for this zwlr_foreign_toplevel_handle_v1. The
        toplevel itself becomes inert so any requests will be ignored except the
        destroy request.
      </description>
    </event>

    <request name="destroy" type="destructor">
      <description summary="destroy the zwlr_foreign_toplevel_handle_v1 object">
        Destroys the zwlr_foreign_toplevel_handle_v1 object.

        This request should be called either when the client does not want to
        use the toplevel anymore or after the closed event to finalize the
        destruction of the object.
      </description>
    </request>

    <!-- Version 2 additions -->

    <request name="set_fullscreen" since="2">
      <description summary="request that the toplevel be fullscreened">
        Requests that the toplevel be fullscreened on the given output. If the
        fullscreen state and/or the outputs the toplevel is visible on actually
        change, this will be indicated by the state and output_enter/leave
        events.

        The output parameter is only a hint to the compositor. Also, if output
        is NULL, the compositor should decide which output the toplevel will be
        fullscreened on, if at all.
      </description>
      <arg name="output" type="object" interface="wl_output" allow-null="true"/>
    </request>

    <request name="unset_fullscreen" since="2">
      <description summary="request that the toplevel be unfullscreened">
        Requests that the toplevel be unfullscreened. If the fullscreen state
        actually changes, this will be indicated by the state event.
      </description>
    </request>

    <!-- Version 3 additions -->

    <event name="parent" since="3">
      <description summary="parent change">
        This event is emitted whenever the parent of the toplevel changes.

        No event is emitted when the parent handle is destroyed by the client.
      </description>
      <arg name="parent" type="object" interface="zwlr_foreign_toplevel_handle_v1" allow-null="true"/>
    </event>
  </interface>
</protocol>

name: Build and Push Docker Image

on:
  workflow_dispatch:
  schedule:
    # run monthly
    - cron: '0 0 1 * *'

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule' || github.repository == 'Alexays/Waybar'
    strategy:
      fail-fast: false # don't fail the other jobs if one of the images fails to build
      matrix:
        os: [ 'alpine', 'archlinux', 'debian', 'fedora', 'gentoo', 'opensuse' ]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfiles/${{ matrix.os }}
          push: true
          tags: alexays/waybar:${{ matrix.os }}

name: update-flake-lock
on:
  workflow_dispatch: # allows manual triggering
  schedule:
    - cron: '0 0 1 * *' # Run monthly
  push:
      paths:
        - 'flake.nix'
jobs:
  lockfile:
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule' || github.repository == 'Alexays/Waybar'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Install Nix
        uses: cachix/install-nix-action@v27
        with:
          extra_nix_config: |
            access-tokens = github.com=${{ secrets.GITHUB_TOKEN }}
      - name: Update flake.lock
        uses: DeterminateSystems/update-flake-lock@v21

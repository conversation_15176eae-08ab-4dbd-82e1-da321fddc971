name: "Nix-<PERSON>"
on:
  pull_request:
  push:
jobs:
  nix-flake-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: cachix/install-nix-action@v27
      with:
        extra_nix_config: |
          experimental-features = nix-command flakes
          access-tokens = github.com=${{ secrets.GITHUB_TOKEN }}
    - run: nix flake show
    - run: nix flake check --print-build-logs
    - run: nix build --print-build-logs

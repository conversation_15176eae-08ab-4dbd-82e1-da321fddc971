name: freebsd

on: [push, pull_request]

concurrency:
  group: ${{ github.workflow }}-freebsd-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    # Run actions in a FreeBSD VM on the ubuntu runner
    # https://github.com/actions/runner/issues/385 - for FreeBSD runner support
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Test in FreeBSD VM
        uses: cross-platform-actions/action@v0.28.0
        timeout-minutes: 180
        env:
          CPPFLAGS: '-isystem/usr/local/include'
          LDFLAGS:  '-L/usr/local/lib'
        with:
          operating_system: freebsd
          version: "14.2"
          environment_variables: CPPFLAGS LDFLAGS
          sync_files: runner-to-vm
          run: |
            sudo pkg install -y git #  subprojects/date
            sudo pkg install -y catch evdev-proto gtk-layer-shell gtkmm30 jsoncpp \
              libdbusmenu libevdev libfmt libmpdclient libudev-devd meson \
              pkgconf pipewire pulseaudio scdoc sndio spdlog wayland-protocols upower \
              libinotify
            meson setup build -Dman-pages=enabled
            ninja -C build
            meson test -C build --no-rebuild --print-errorlogs --suite waybar

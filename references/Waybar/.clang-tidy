Checks: >
  -*,
  bugprone-*
  misc-*,
  modernize-*,
  performance-*,
  portability-*,
  readability-*,
  -fuchsia-trailing-return,
  -readability-magic-numbers,
  -modernize-use-nodiscard,
  -modernize-use-trailing-return-type,
  -readability-braces-around-statements,
  -readability-redundant-access-specifiers,
  -readability-redundant-member-init,
  -readability-redundant-string-init,
  -readability-identifier-length
# CheckOptions:
#   - { key: readability-identifier-naming.NamespaceCase,          value: lower_case }
#   - { key: readability-identifier-naming.ClassCase,              value: CamelCase  }
#   - { key: readability-identifier-naming.StructCase,             value: CamelCase  }
#   - { key: readability-identifier-naming.FunctionCase,           value: camelBack  }
#   - { key: readability-identifier-naming.VariableCase,           value: camelBack  }
#   - { key: readability-identifier-naming.PrivateMemberCase,      value: camelBack  }
#   - { key: readability-identifier-naming.PrivateMemberSuffix,    value: _          }
#   - { key: readability-identifier-naming.EnumCase,               value: CamelCase  }
#   - { key: readability-identifier-naming.EnumConstantCase,       value: UPPER_CASE }
#   - { key: readability-identifier-naming.GlobalConstantCase,     value: UPPER_CASE }
#   - { key: readability-identifier-naming.StaticConstantCase,     value: UPPER_CASE }
